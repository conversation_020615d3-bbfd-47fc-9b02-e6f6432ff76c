import os


def count_lines(directory, extensions):
    total_lines = 0
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(extensions):
                with open(os.path.join(root, file), 'r', encoding='utf-8', errors='ignore') as f:
                    # count only non-empty lines
                    total_lines += sum(1 for line in f if line.strip())
    return total_lines


def count_lines_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return sum(1 for line in f if line.strip())


# Directories in your project
base = "C:\\Users\\<USER>\\PycharmProjects\\journal"
models = os.path.join(base, "models")
services = os.path.join(base, "services")
dash_app = os.path.join(base, "dash_app")

# Count by type
python_lines = (
    count_lines(models, ".py") +
    count_lines(services, ".py") +
    count_lines(dash_app, ".py") +
    count_lines_file(os.path.join(base, "cdos.py")) +
    count_lines_file(os.path.join(base, "claude.py")) +
    count_lines_file(os.path.join(base, "helper.py")) +
    # count_lines_file(os.path.join(base, "MEMEBot.py")) +
    count_lines_file(os.path.join(base, "trades_db.py")) +
    count_lines_file(os.path.join(base, "trades_db_tables.py"))
)

js_lines = (
    count_lines(models, ".js") +
    count_lines(services, ".js") +
    count_lines(dash_app, ".js")
)

css_lines = (
    count_lines(models, ".css") +
    count_lines(services, ".css") +
    count_lines(dash_app, ".css")
)

# Print results
print(f"Total Python lines: {python_lines}")
print(f"Total JS lines: {js_lines}")
print(f"Total CSS lines: {css_lines}")

print(f"\nGrand Total (Python + JS + CSS): {python_lines + js_lines + css_lines}")
