"""
Comprehensive tests for Bybit order management functionality.

Tests cover:
- Order submission (market, limit, stop-loss, take-profit)
- Order cancellation and updates
- Trade execution from SETUP status
- Error handling and rate limiting
- Environment switching
- Integration with TradeCraft system
"""

import unittest
import time
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.bybit import Bybit
from services.bybit_config import Environment, setup_testnet, setup_mainnet
from services.order_execution_service import OrderExecutionService, OrderExecutionResult
from services.api_utils import BybitAPIError, BybitRateLimits
from models.trade import Trade, TradeStatus, TradeDirection, Exchange
from models.order import Order, BuySell
from services.trade_creation_service import OrderType, OrderStatus


class TestBybitOrderManagement(unittest.TestCase):
    """Test Bybit order management functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Use testnet for all tests
        setup_testnet()
        
        # Reset rate limiters
        for limiter in BybitRateLimits.LIMITS.values():
            limiter.calls.clear()
    
    def test_environment_switching(self):
        """Test switching between testnet and mainnet"""
        # Test testnet setup
        testnet_config = setup_testnet()
        self.assertTrue(testnet_config.is_testnet)
        self.assertFalse(testnet_config.is_mainnet)
        
        # Test mainnet setup
        mainnet_config = setup_mainnet()
        self.assertTrue(mainnet_config.is_mainnet)
        self.assertFalse(mainnet_config.is_testnet)
    
    @patch('services.bybit.requests.post')
    def test_submit_market_order(self, mock_post):
        """Test market order submission"""
        # Mock successful response
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = {
            "retCode": 0,
            "retMsg": "OK",
            "result": {
                "orderId": "test_order_123",
                "orderLinkId": "test_link_123"
            }
        }
        mock_post.return_value = mock_response
        
        # Submit market order
        result = Bybit.submit_order(
            symbol="BTCUSDT",
            side="Buy",
            order_type="Market",
            qty="0.001"
        )
        
        # Verify request
        self.assertTrue(mock_post.called)
        call_args = mock_post.call_args
        payload = call_args[1]['json']
        
        self.assertEqual(payload['symbol'], 'BTCUSDT')
        self.assertEqual(payload['side'], 'Buy')
        self.assertEqual(payload['orderType'], 'Market')
        self.assertEqual(payload['qty'], '0.001')
        
        # Verify response
        self.assertEqual(result['retCode'], 0)
        self.assertEqual(result['result']['orderId'], 'test_order_123')
    
    @patch('services.bybit.requests.post')
    def test_submit_limit_order(self, mock_post):
        """Test limit order submission"""
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = {
            "retCode": 0,
            "retMsg": "OK",
            "result": {"orderId": "limit_order_123"}
        }
        mock_post.return_value = mock_response
        
        # Submit limit order
        result = Bybit.submit_order(
            symbol="ETHUSDT",
            side="Sell",
            order_type="Limit",
            qty="0.1",
            price="2000.50"
        )
        
        # Verify limit order specific parameters
        call_args = mock_post.call_args
        payload = call_args[1]['json']
        
        self.assertEqual(payload['price'], '2000.50')
        self.assertEqual(result['retCode'], 0)
    
    def test_submit_order_validation(self):
        """Test order submission parameter validation"""
        # Test missing required parameters
        with self.assertRaises(BybitAPIError) as context:
            Bybit.submit_order(symbol="", side="Buy", order_type="Market", qty="0.001")
        self.assertIn("Missing required parameters", str(context.exception))
        
        # Test limit order without price
        with self.assertRaises(BybitAPIError) as context:
            Bybit.submit_order(symbol="BTCUSDT", side="Buy", order_type="Limit", qty="0.001")
        self.assertIn("Price is required for Limit orders", str(context.exception))
    
    @patch('services.bybit.requests.post')
    def test_cancel_order(self, mock_post):
        """Test order cancellation"""
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = {
            "retCode": 0,
            "retMsg": "OK",
            "result": {"orderId": "cancelled_order_123"}
        }
        mock_post.return_value = mock_response
        
        # Cancel order by ID
        result = Bybit.cancel_order(
            symbol="BTCUSDT",
            order_id="test_order_123"
        )
        
        # Verify request
        call_args = mock_post.call_args
        payload = call_args[1]['json']
        
        self.assertEqual(payload['symbol'], 'BTCUSDT')
        self.assertEqual(payload['orderId'], 'test_order_123')
        self.assertEqual(result['retCode'], 0)
    
    def test_cancel_order_validation(self):
        """Test order cancellation validation"""
        # Test missing order identifier
        with self.assertRaises(BybitAPIError) as context:
            Bybit.cancel_order(symbol="BTCUSDT")
        self.assertIn("Either order_id or order_link_id must be provided", str(context.exception))
        
        # Test missing symbol
        with self.assertRaises(BybitAPIError) as context:
            Bybit.cancel_order(symbol="", order_id="test_123")
        self.assertIn("Symbol is required", str(context.exception))
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Test order submission rate limit
        limiter = BybitRateLimits.LIMITS["order_submit"]
        
        # Fill up the rate limit
        for _ in range(limiter.max_calls):
            limiter.add_call()
        
        # Next call should be rate limited
        self.assertFalse(limiter.is_allowed())
        self.assertGreater(limiter.wait_time(), 0)
        
        # Wait and try again
        time.sleep(0.1)
        self.assertTrue(limiter.is_allowed())
    
    @patch('services.bybit.requests.post')
    def test_api_error_handling(self, mock_post):
        """Test API error handling"""
        # Mock API error response
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = {
            "retCode": 10001,
            "retMsg": "Parameter error"
        }
        mock_post.return_value = mock_response
        
        # Should raise BybitAPIError
        with self.assertRaises(BybitAPIError) as context:
            Bybit.submit_order(
                symbol="BTCUSDT",
                side="Buy",
                order_type="Market",
                qty="0.001"
            )
        
        self.assertIn("Parameter error", str(context.exception))
        self.assertEqual(context.exception.error_code, "10001")


class TestOrderExecutionService(unittest.TestCase):
    """Test order execution service"""
    
    def setUp(self):
        """Set up test environment"""
        setup_testnet()
    
    def create_test_trade(self) -> Trade:
        """Create a test trade with SETUP status"""
        # Create test orders
        orders = []
        
        # Entry order
        entry_order = Order(
            id_field=None,
            order_id="entry_123",
            trade_id=None,
            created_date=None,
            filled_date=None,
            symbol="BTCUSDT",
            orderType=OrderType.ENTRY,
            orderStatus=OrderStatus.SETUP,
            buySell=BuySell.BUY,
            reduce=False,
            price=Decimal("50000"),
            fillPrice=Decimal("0"),
            fee=Decimal("0"),
            quantity=Decimal("0.001"),
            filledQuantity=Decimal("0"),
            sierraActivity=None,
            coinbaseOrder=None,
            coinbaseFill=None,
            bybitOrder=None
        )
        orders.append(entry_order)
        
        # Stop-loss order
        sl_order = Order(
            id_field=None,
            order_id="sl_123",
            trade_id=None,
            created_date=None,
            filled_date=None,
            symbol="BTCUSDT",
            orderType=OrderType.STOP_LOSS,
            orderStatus=OrderStatus.SETUP,
            buySell=BuySell.SELL,
            reduce=True,
            price=Decimal("48000"),
            fillPrice=Decimal("0"),
            fee=Decimal("0"),
            quantity=Decimal("0.001"),
            filledQuantity=Decimal("0"),
            sierraActivity=None,
            coinbaseOrder=None,
            coinbaseFill=None,
            bybitOrder=None
        )
        orders.append(sl_order)
        
        # Create trade
        trade = Trade(
            id_field=None,
            exchange_trade_id="test_trade_123",
            trade_orders=orders,
            unfilled_orders=[],
            symbol="BTCUSDT",
            accountBalance=Decimal("10000"),
            exchange=Exchange.BYBIT,
            direction=TradeDirection.LONG,
            timeOpen=None,
            status=TradeStatus.SETUP,
            tradeQty=None,
            openQty=None,
            lastUpdate=None,
            timeClose=None,
            duration=None,
            chartLink=None,
            notes="Test trade",
            notional=None,
            leverage=None,
            avgOpenPrice=None,
            avgClosePrice=None,
            riskAmt=None,
            riskPercent=None,
            profit=Decimal("0"),
            fees=None,
            strategy="Test",
            time_frame="1h",
            username="test_user"
        )
        
        return trade
    
    def test_trade_status_validation(self):
        """Test trade status validation"""
        trade = self.create_test_trade()
        trade.status = TradeStatus.OPEN  # Wrong status
        
        result = OrderExecutionService.execute_setup_trade(trade)
        
        self.assertFalse(result.success)
        self.assertIn("expected SETUP", result.error)
    
    @patch('services.bybit.Bybit.submit_order')
    @patch('services.bybit.Bybit.submit_stop_loss')
    def test_successful_trade_execution(self, mock_submit_sl, mock_submit_order):
        """Test successful trade execution"""
        # Mock successful responses
        mock_submit_order.return_value = {
            "retCode": 0,
            "result": {"orderId": "executed_entry_123"}
        }
        mock_submit_sl.return_value = {
            "retCode": 0,
            "result": {"orderId": "executed_sl_123"}
        }
        
        trade = self.create_test_trade()
        result = OrderExecutionService.execute_setup_trade(trade)
        
        # Verify execution
        self.assertTrue(result.success)
        self.assertEqual(len(result.executed_orders), 2)  # Entry + Stop-loss
        self.assertEqual(len(result.failed_orders), 0)
        
        # Verify trade status updated
        self.assertEqual(trade.status, TradeStatus.OPEN)
    
    @patch('services.bybit.Bybit.submit_order')
    def test_partial_trade_execution(self, mock_submit_order):
        """Test partial trade execution (some orders fail)"""
        # Mock mixed responses
        def side_effect(*args, **kwargs):
            if "entry" in kwargs.get('order_link_id', ''):
                return {"retCode": 0, "result": {"orderId": "success_123"}}
            else:
                raise BybitAPIError("Order failed")
        
        mock_submit_order.side_effect = side_effect
        
        trade = self.create_test_trade()
        result = OrderExecutionService.execute_setup_trade(trade)
        
        # Should still be successful with partial execution
        self.assertTrue(result.success)
        self.assertGreater(len(result.executed_orders), 0)
        self.assertGreater(len(result.failed_orders), 0)
        self.assertIn("Partial execution", result.error)


class TestIntegrationWithTradeCraft(unittest.TestCase):
    """Test integration with existing TradeCraft system"""
    
    def test_fibonacci_trade_creation_and_execution(self):
        """Test end-to-end Fibonacci trade creation and execution"""
        # This would test the full flow from chart_api.py
        # through trade creation to order execution
        
        # Mock Fibonacci trade data
        fib_data = {
            "symbol": "BTCUSDT",
            "exchange": "bybit",
            "timeframe": "1h",
            "p1": 48000.0,  # Stop loss
            "p2": 52000.0,  # High
            "fib50": 50000.0,   # 50% entry
            "fib618": 49500.0,  # 61.8% entry
            "fib786": 49000.0,  # 78.6% entry
            "fibtp236": 51000.0,  # 23.6% TP
            "fibtp382": 51500.0,  # 38.2% TP
        }
        
        # Test trade creation
        from services.trade_creation_service import TradeCreationService
        trade = TradeCreationService.create_fibonacci_trade(fib_data, Decimal("10000"))
        
        # Verify trade structure
        self.assertEqual(trade.status, TradeStatus.SETUP)
        self.assertEqual(trade.symbol, "BTCUSDT")
        self.assertEqual(len(trade.trade_orders), 7)  # 1 SL + 3 entries + 2 TPs + 1 TP
        
        # Verify getTotalQuantity uses quantity for SETUP trades
        total_qty = trade.getTotalQuantity()
        self.assertEqual(total_qty, Decimal("6"))  # Total position size
    
    def test_trade_quantity_calculation(self):
        """Test that getTotalQuantity uses quantity for SETUP trades"""
        trade = Trade(
            id_field=None,
            exchange_trade_id="test_123",
            trade_orders=[],
            unfilled_orders=[],
            symbol="BTCUSDT",
            accountBalance=Decimal("10000"),
            exchange=Exchange.BYBIT,
            direction=TradeDirection.LONG,
            timeOpen=None,
            status=TradeStatus.SETUP,  # SETUP status
            tradeQty=None,
            openQty=None,
            lastUpdate=None,
            timeClose=None,
            duration=None,
            chartLink=None,
            notes=None,
            notional=None,
            leverage=None,
            avgOpenPrice=None,
            avgClosePrice=None,
            riskAmt=None,
            riskPercent=None,
            profit=Decimal("0"),
            fees=None,
            strategy=None,
            time_frame=None,
            username="test"
        )
        
        # Add test order
        order = Order(
            id_field=None,
            order_id="test_order",
            trade_id=None,
            created_date=None,
            filled_date=None,
            symbol="BTCUSDT",
            orderType=OrderType.ENTRY,
            orderStatus=OrderStatus.SETUP,
            buySell=BuySell.BUY,
            reduce=False,
            price=Decimal("50000"),
            fillPrice=Decimal("0"),
            fee=Decimal("0"),
            quantity=Decimal("0.1"),      # Planned quantity
            filledQuantity=Decimal("0"),  # No fills yet
            sierraActivity=None,
            coinbaseOrder=None,
            coinbaseFill=None,
            bybitOrder=None
        )
        trade.trade_orders = [order]
        
        # For SETUP trades, should use quantity (0.1), not filledQuantity (0)
        total_qty = trade.getTotalQuantity()
        self.assertEqual(total_qty, Decimal("0.1"))
        
        # Change to OPEN status - should use filledQuantity
        trade.status = TradeStatus.OPEN
        total_qty = trade.getTotalQuantity()
        self.assertEqual(total_qty, Decimal("0"))  # Uses filledQuantity


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
