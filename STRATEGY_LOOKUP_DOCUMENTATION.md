# Strategy Lookup/Create Functionality

## Overview

The `get_or_create_strategy_by_name()` function has been added to the `TradesDB` class in `trades_db.py` to provide a robust way to ensure strategies exist in the database before associating them with trades.

## Function Signature

```python
@staticmethod
def get_or_create_strategy_by_name(strategy_name):
    """
    Looks up a strategy by name. If not found, creates a new strategy record.
    
    Args:
        strategy_name (str): The name of the strategy to find or create
        
    Returns:
        int: The strategy ID (either existing or newly created)
        
    Raises:
        ValueError: If strategy_name is empty or None
        Exception: If database operation fails
    """
```

## Key Features

### 1. **Lookup-or-Create Pattern**
- First attempts to find an existing strategy by name
- If found, returns the existing strategy ID
- If not found, creates a new strategy record with sensible defaults
- Returns the newly created strategy ID

### 2. **Input Validation**
- Validates that `strategy_name` is not empty, None, or whitespace-only
- Strips whitespace from strategy names for consistency
- Raises `ValueError` for invalid inputs

### 3. **Error Handling**
- Uses try-catch blocks to handle database errors gracefully
- Provides detailed error messages for debugging
- Follows existing error handling patterns in the codebase

### 4. **Database Transaction Safety**
- Uses the existing database connection patterns from `trades_db.py`
- Commits transactions properly
- Follows the established database operation patterns

## Usage Examples

### Basic Usage

```python
from trades_db import TradesDB

# Ensure a strategy exists and get its ID
strategy_id = TradesDB.get_or_create_strategy_by_name("Breakout Strategy")
print(f"Strategy ID: {strategy_id}")

# Use the strategy ID when creating/updating trades
trade.strategy = str(strategy_id)  # or add to existing strategy list
TradesDB.saveTrade(trade)
```

### In Trade Processing Workflow

```python
def process_trade_with_strategy(trade_data, strategy_name):
    """Example of using strategy lookup in trade processing."""
    
    try:
        # Step 1: Ensure the strategy exists (create if needed)
        strategy_id = TradesDB.get_or_create_strategy_by_name(strategy_name)
        
        # Step 2: Create/update the trade with the strategy ID
        trade = create_trade_from_data(trade_data)
        trade.strategy = str(strategy_id)
        
        # Step 3: Save the trade
        TradesDB.saveTrade(trade)
        
        print(f"✅ Trade processed with strategy '{strategy_name}' (ID: {strategy_id})")
        
    except ValueError as e:
        print(f"❌ Invalid strategy name: {e}")
    except Exception as e:
        print(f"❌ Error processing trade: {e}")
```

### Batch Processing

```python
def process_multiple_trades(trade_list):
    """Process multiple trades, ensuring all strategies exist."""
    
    strategy_cache = {}  # Cache strategy IDs to avoid repeated lookups
    
    for trade_data in trade_list:
        strategy_name = trade_data.get('strategy_name')
        
        if strategy_name:
            # Check cache first
            if strategy_name not in strategy_cache:
                strategy_id = TradesDB.get_or_create_strategy_by_name(strategy_name)
                strategy_cache[strategy_name] = strategy_id
            
            # Use cached strategy ID
            trade_data['strategy_id'] = strategy_cache[strategy_name]
        
        # Process the trade...
```

## Default Values for New Strategies

When creating a new strategy, the function sets these default values:

- **name**: The provided strategy name (trimmed)
- **display_name**: Same as name
- **description**: Empty string
- **notes**: Empty string  
- **images**: Empty string
- **is_active**: True
- **createdDate**: Current timestamp in milliseconds
- **modifiedDate**: Current timestamp in milliseconds

## Integration with Existing Code

### With `save_trade_strategy()`

The new function works seamlessly with the existing `save_trade_strategy()` function:

```python
# Method 1: Use strategy ID directly
strategy_id = TradesDB.get_or_create_strategy_by_name("My Strategy")
TradesDB.save_trade_strategy([strategy_id], trade_id=trade_id)

# Method 2: Use strategy name (existing functionality)
TradesDB.save_trade_strategy(["My Strategy"], trade_id=trade_id)
```

### With Trade Creation

```python
# Ensure strategy exists before creating trade
strategy_id = TradesDB.get_or_create_strategy_by_name("Momentum Play")

# Create trade with strategy reference
trade = Trade(...)
trade.strategy = str(strategy_id)  # Store as string (existing pattern)
TradesDB.saveTrade(trade)
```

## Error Handling

### Input Validation Errors

```python
try:
    strategy_id = TradesDB.get_or_create_strategy_by_name("")
except ValueError as e:
    print(f"Invalid input: {e}")  # "Strategy name cannot be empty"
```

### Database Errors

```python
try:
    strategy_id = TradesDB.get_or_create_strategy_by_name("My Strategy")
except Exception as e:
    print(f"Database error: {e}")
    # Handle database connection issues, constraint violations, etc.
```

## Testing

The functionality has been thoroughly tested with:

1. **Basic lookup/create operations**
2. **Duplicate strategy handling** (lookup existing)
3. **Input validation** (empty strings, None values, whitespace)
4. **Error handling** (database errors, constraint violations)
5. **Integration testing** with existing database functions

Run the test with:
```bash
python test_strategy_standalone.py
```

## Performance Considerations

- **Caching**: For batch operations, consider caching strategy IDs to avoid repeated database lookups
- **Transactions**: The function commits each operation individually. For bulk operations, consider using database transactions
- **Indexing**: The Strategy table has a unique index on the `name` column for fast lookups

## Database Schema

The function works with the existing Strategy table schema:

```sql
CREATE TABLE Strategy (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    notes TEXT,
    images TEXT,
    sort_order INTEGER,
    is_active BOOLEAN DEFAULT 1,
    createdDate INTEGER,
    modifiedDate INTEGER,
    username TEXT
);
```

## Best Practices

1. **Always validate strategy names** before calling the function
2. **Cache strategy IDs** when processing multiple trades with the same strategy
3. **Handle exceptions** appropriately in your trade processing workflow
4. **Use meaningful strategy names** that will be helpful for analysis
5. **Consider using transactions** for bulk operations involving multiple trades and strategies
