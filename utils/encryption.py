"""
Encryption utilities for securing sensitive data like API secrets.

Uses Fernet symmetric encryption from the cryptography library.
"""

import os
import base64
import logging
from typing import Optional
from cryptography.fernet import <PERSON>rne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)


class EncryptionManager:
    """
    Manages encryption and decryption of sensitive data using Fernet symmetric encryption.
    
    The encryption key is derived from a master password using PBKDF2 with a salt.
    This provides a secure way to encrypt API secrets before storing them in the database.
    """
    
    def __init__(self, master_password: Optional[str] = None):
        """
        Initialize the encryption manager.
        
        Args:
            master_password: Master password for encryption. If None, uses environment variable.
        """
        self._master_password = master_password or os.environ.get("ENCRYPTION_KEY", "default_master_key_change_in_production")
        self._salt = b'tradecraft_salt_2024'  # Fixed salt for consistency
        self._fernet = None
        self._initialize_fernet()
    
    def _initialize_fernet(self):
        """Initialize the Fernet cipher with a key derived from the master password."""
        try:
            # Derive a key from the master password using PBKDF2
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self._salt,
                iterations=100000,  # Recommended minimum iterations
            )
            key = base64.urlsafe_b64encode(kdf.derive(self._master_password.encode()))
            self._fernet = Fernet(key)
            logger.info("🔐 Encryption manager initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize encryption: {e}")
            raise
    
    def encrypt(self, plaintext: str) -> str:
        """
        Encrypt a plaintext string.
        
        Args:
            plaintext: The string to encrypt
            
        Returns:
            Base64-encoded encrypted string
            
        Raises:
            Exception: If encryption fails
        """
        if not plaintext:
            return ""
        
        try:
            encrypted_bytes = self._fernet.encrypt(plaintext.encode())
            return base64.urlsafe_b64encode(encrypted_bytes).decode()
        except Exception as e:
            logger.error(f"❌ Encryption failed: {e}")
            raise Exception(f"Failed to encrypt data: {e}")
    
    def decrypt(self, encrypted_text: str) -> str:
        """
        Decrypt an encrypted string.
        
        Args:
            encrypted_text: Base64-encoded encrypted string
            
        Returns:
            Decrypted plaintext string
            
        Raises:
            Exception: If decryption fails
        """
        if not encrypted_text:
            return ""
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_text.encode())
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception as e:
            logger.error(f"❌ Decryption failed: {e}")
            raise Exception(f"Failed to decrypt data: {e}")
    
    def is_encrypted(self, text: str) -> bool:
        """
        Check if a string appears to be encrypted (basic heuristic).
        
        Args:
            text: String to check
            
        Returns:
            True if the string appears to be encrypted
        """
        if not text:
            return False
        
        try:
            # Try to decode as base64 - encrypted strings should be base64 encoded
            base64.urlsafe_b64decode(text.encode())
            # If it decodes successfully and has reasonable length, likely encrypted
            return len(text) > 20 and '=' in text[-4:]  # Base64 padding
        except Exception:
            return False


# Global encryption manager instance
_encryption_manager = None


def get_encryption_manager() -> EncryptionManager:
    """
    Get the global encryption manager instance.
    
    Returns:
        EncryptionManager: Global instance
    """
    global _encryption_manager
    if _encryption_manager is None:
        _encryption_manager = EncryptionManager()
    return _encryption_manager


def encrypt_api_secret(api_secret: str) -> str:
    """
    Convenience function to encrypt an API secret.
    
    Args:
        api_secret: Plain text API secret
        
    Returns:
        Encrypted API secret
    """
    return get_encryption_manager().encrypt(api_secret)


def decrypt_api_secret(encrypted_secret: str) -> str:
    """
    Convenience function to decrypt an API secret.
    
    Args:
        encrypted_secret: Encrypted API secret
        
    Returns:
        Plain text API secret
    """
    return get_encryption_manager().decrypt(encrypted_secret)


def is_secret_encrypted(secret: str) -> bool:
    """
    Check if an API secret is encrypted.
    
    Args:
        secret: API secret to check
        
    Returns:
        True if the secret appears to be encrypted
    """
    return get_encryption_manager().is_encrypted(secret)


# Example usage:
if __name__ == "__main__":
    # Test the encryption functionality
    manager = EncryptionManager()
    
    test_secret = "my_super_secret_api_key_12345"
    print(f"Original: {test_secret}")
    
    encrypted = manager.encrypt(test_secret)
    print(f"Encrypted: {encrypted}")
    
    decrypted = manager.decrypt(encrypted)
    print(f"Decrypted: {decrypted}")
    
    print(f"Match: {test_secret == decrypted}")
    print(f"Is encrypted: {manager.is_encrypted(encrypted)}")
    print(f"Is original encrypted: {manager.is_encrypted(test_secret)}")
