from __future__ import annotations

from trades_db import get_db_cursor, get_db_connection

"""
🔧 Chart API Server Management:
This can run locally or on a remote server. (next to main app)

# 🚀 Start the server:
uvicorn services.chart_api:app --host 0.0.0.0 --port 8900

# 🔄 Start with auto-reload (recommended for development):
uvicorn services.chart_api:app --host 0.0.0.0 --port 8900 --reload

# 🛑 Stop the server:
Press Ctrl+C in the terminal where it's running

# 🔍 Check if server is running:
curl "http://localhost:8900/candles?exchange=binance&symbol=BTCUSDT&timeframe=1h&limit=1"
Browser: "http://localhost:8900/candles/historical?exchange=binance&symbol=BTCUSDT&timeframe=1h&before=1753153200000&limit=10"

# 📊 Available endpoints:
- GET /candles?exchange=binance&symbol=BTCUSDT&timeframe=1h&limit=1000
- GET /candles/historical?exchange=binance&symbol=BTCUSDT&timeframe=1h&before=1640995200000&limit=500
- GET /symbols?exchange=binance

# 📝 Notes:
- Server must be running for charting interface to load market data
- Uses Binance US API (no geo-blocking issues)
- Serves data to charting.html via localhost:8900
"""

from fastapi import FastAPI, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from services.candle_service import CandleService

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

svc = CandleService()


@app.get("/candles")
def candles(exchange: str = "binance", symbol: str = "BTCUSDT", timeframe: str = "1h",
            limit: int = 1000, start: int | None = None, end: int | None = None):
    try:
        print(f"[CHART_API] GET /candles - exchange={exchange}, symbol={symbol}, timeframe={timeframe}, limit={limit}")
        df = svc.get_candles(exchange, symbol, timeframe, limit, start, end)
        if df is None or df.empty:
            print(f"[CHART_API] Empty dataframe returned for {exchange}/{symbol}/{timeframe}")
            return []
        print(f"[CHART_API] Returning {len(df)} candles for {exchange}/{symbol}/{timeframe}")
        return [
            {
                "time": int(ts.timestamp()),
                "open": float(r["open"]),
                "high": float(r["high"]),
                "low": float(r["low"]),
                "close": float(r["close"]),
                "volume": float(r.get("volume", 0.0)),
            }
            for ts, r in df.iterrows()
        ]
    except Exception as e:
        import traceback
        print(f"[CHART_API] ERROR in /candles - exchange={exchange}, symbol={symbol}, timeframe={timeframe}")
        print(f"[CHART_API] Exception: {type(e).__name__}: {str(e)}")
        print(f"[CHART_API] Traceback:")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/candles/historical")
def candles_hist(exchange: str = "binance", symbol: str = "BTCUSDT", timeframe: str = "1h",
                 before: int = Query(...), limit: int = 1000):
    print(f"[CHART_API] GET /candles/historical - exchange={exchange}, symbol={symbol}, timeframe={timeframe}, before={before}, limit={limit}")
    return candles(exchange=exchange, symbol=symbol, timeframe=timeframe, limit=limit, start=None, end=before)


@app.get("/symbols")
def symbols(exchange: str = "binance"):
    try:
        print(f"[CHART_API] GET /symbols - exchange={exchange}")
        result = svc.get_symbols(exchange)
        print(f"[CHART_API] Returning {len(result) if result else 0} symbols for {exchange}")
        return result
    except Exception as e:
        import traceback
        print(f"[CHART_API] ERROR in /symbols - exchange={exchange}")
        print(f"[CHART_API] Exception: {type(e).__name__}: {str(e)}")
        print(f"[CHART_API] Traceback:")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))