"""
API Utilities

Provides rate limiting, error handling, and retry mechanisms for API calls.
"""

import functools
import logging
import time
from collections import deque
from datetime import datetime
from typing import Callable

import requests

logger = logging.getLogger(__name__)


class RateLimiter:
    """Rate limiter for API calls"""
    
    def __init__(self, max_calls: int, time_window: int):
        """
        Initialize rate limiter.
        
        Args:
            max_calls: Maximum number of calls allowed
            time_window: Time window in seconds
        """
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = deque()
    
    def is_allowed(self) -> bool:
        """Check if a call is allowed under rate limit"""
        now = time.time()
        
        # Remove old calls outside the time window
        while self.calls and self.calls[0] <= now - self.time_window:
            self.calls.popleft()
        
        # Check if we're under the limit
        return len(self.calls) < self.max_calls
    
    def add_call(self):
        """Record a new API call"""
        self.calls.append(time.time())
    
    def wait_time(self) -> float:
        """Get time to wait before next call is allowed"""
        if not self.calls:
            return 0.0
        
        oldest_call = self.calls[0]
        wait_time = (oldest_call + self.time_window) - time.time()
        return max(0.0, wait_time)


class BybitRateLimits:
    """Bybit API rate limits"""
    
    # Different rate limits for different endpoints
    LIMITS = {
        "order_submit": RateLimiter(10, 1),      # 10 orders per second
        "order_cancel": RateLimiter(10, 1),      # 10 cancels per second
        "order_query": RateLimiter(50, 1),       # 50 queries per second
        "position_query": RateLimiter(50, 1),    # 50 position queries per second
        "general": RateLimiter(120, 60),         # 120 calls per minute for other endpoints
    }
    
    @classmethod
    def check_rate_limit(cls, endpoint_type: str = "general") -> bool:
        """Check if call is allowed for endpoint type"""
        limiter = cls.LIMITS.get(endpoint_type, cls.LIMITS["general"])
        return limiter.is_allowed()
    
    @classmethod
    def record_call(cls, endpoint_type: str = "general"):
        """Record an API call"""
        limiter = cls.LIMITS.get(endpoint_type, cls.LIMITS["general"])
        limiter.add_call()
    
    @classmethod
    def get_wait_time(cls, endpoint_type: str = "general") -> float:
        """Get wait time for endpoint type"""
        limiter = cls.LIMITS.get(endpoint_type, cls.LIMITS["general"])
        return limiter.wait_time()


class BybitAPIError(Exception):
    """Custom exception for Bybit API errors"""
    
    def __init__(self, message: str, error_code: str = None, response_data: dict = None):
        super().__init__(message)
        self.error_code = error_code
        self.response_data = response_data
        self.timestamp = datetime.now()


class APIErrorHandler:
    """Handles API errors and provides meaningful error messages"""
    
    # Common Bybit error codes and their meanings
    ERROR_CODES = {
        "10001": "Parameter error - Check your request parameters",
        "10002": "Invalid request - Request format is incorrect",
        "10003": "Invalid API key - Check your API credentials",
        "10004": "Invalid timestamp - Check system time synchronization",
        "10005": "Invalid signature - Check API secret and signature generation",
        "10006": "Permission denied - API key lacks required permissions",
        "10007": "Too many requests - Rate limit exceeded",
        "10008": "Invalid symbol - Trading pair not supported",
        "10009": "Invalid order type - Order type not allowed",
        "10010": "Invalid order side - Buy/Sell parameter incorrect",
        "10011": "Invalid quantity - Order quantity out of range",
        "10012": "Invalid price - Order price out of range",
        "10013": "Insufficient balance - Not enough funds for order",
        "10014": "Order not found - Order ID does not exist",
        "10015": "Order cannot be cancelled - Order already filled or cancelled",
        "10016": "Position not found - No position exists for symbol",
        "10017": "Risk limit exceeded - Order would exceed risk limits",
        "10018": "Market closed - Trading not allowed at this time",
        "10019": "Liquidation in progress - Cannot place orders during liquidation",
        "10020": "Position mode error - Incorrect position mode setting",
    }
    
    @classmethod
    def handle_response(cls, response: requests.Response, endpoint_type: str = "general") -> dict:
        """
        Handle API response and raise appropriate errors.
        
        Args:
            response: HTTP response object
            endpoint_type: Type of endpoint for rate limiting
            
        Returns:
            dict: Parsed response data
            
        Raises:
            BybitAPIError: If API returns an error
        """
        try:
            data = response.json()
        except ValueError:
            raise BybitAPIError(
                f"Invalid JSON response: {response.text[:200]}",
                error_code="INVALID_JSON",
                response_data={"status_code": response.status_code, "text": response.text[:200]}
            )
        
        # Check for HTTP errors
        if not response.ok:
            error_msg = f"HTTP {response.status_code}: {response.reason}"
            if data and isinstance(data, dict):
                api_error = data.get("retMsg", "Unknown API error")
                error_msg += f" - {api_error}"
            
            raise BybitAPIError(
                error_msg,
                error_code=str(response.status_code),
                response_data=data
            )
        
        # Check for API-level errors
        if isinstance(data, dict):
            ret_code = data.get("retCode")
            if ret_code and ret_code != 0:
                error_code = str(ret_code)
                error_msg = data.get("retMsg", "Unknown error")
                
                # Get more descriptive error message if available
                if error_code in cls.ERROR_CODES:
                    descriptive_msg = cls.ERROR_CODES[error_code]
                    error_msg = f"{descriptive_msg} (Code: {error_code}) - {error_msg}"
                
                raise BybitAPIError(
                    error_msg,
                    error_code=error_code,
                    response_data=data
                )
        
        return data
    
    @classmethod
    def get_user_friendly_error(cls, error: Exception) -> str:
        """Get user-friendly error message"""
        if isinstance(error, BybitAPIError):
            return str(error)
        elif isinstance(error, requests.exceptions.ConnectionError):
            return "Connection error - Please check your internet connection"
        elif isinstance(error, requests.exceptions.Timeout):
            return "Request timeout - Please try again"
        elif isinstance(error, requests.exceptions.RequestException):
            return f"Network error: {str(error)}"
        else:
            return f"Unexpected error: {str(error)}"


def with_rate_limiting(endpoint_type: str = "general"):
    """Decorator to add rate limiting to API calls"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check rate limit
            if not BybitRateLimits.check_rate_limit(endpoint_type):
                wait_time = BybitRateLimits.get_wait_time(endpoint_type)
                logger.warning(f"⏳ Rate limit reached for {endpoint_type}, waiting {wait_time:.2f}s")
                time.sleep(wait_time)
            
            # Record the call
            BybitRateLimits.record_call(endpoint_type)
            
            # Execute the function
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def with_retry(max_retries: int = 3, backoff_factor: float = 1.0, 
               retry_on: tuple = (requests.exceptions.RequestException,)):
    """Decorator to add retry logic to API calls"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except retry_on as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = backoff_factor * (2 ** attempt)
                        logger.warning(f"⚠️ Attempt {attempt + 1} failed, retrying in {wait_time}s: {e}")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"❌ All {max_retries + 1} attempts failed")
                except Exception as e:
                    # Don't retry on non-network errors
                    raise e
            
            # If we get here, all retries failed
            raise last_exception
        
        return wrapper
    return decorator


def with_error_handling(endpoint_type: str = "general"):
    """Decorator to add comprehensive error handling to API calls"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BybitAPIError:
                # Re-raise Bybit API errors as-is
                raise
            except requests.exceptions.RequestException as e:
                # Convert requests exceptions to BybitAPIError
                error_msg = APIErrorHandler.get_user_friendly_error(e)
                raise BybitAPIError(error_msg, error_code="NETWORK_ERROR") from e
            except Exception as e:
                # Convert other exceptions to BybitAPIError
                error_msg = f"Unexpected error in {func.__name__}: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise BybitAPIError(error_msg, error_code="UNEXPECTED_ERROR") from e
        
        return wrapper
    return decorator


# Combined decorator for full API protection
def api_call(endpoint_type: str = "general", max_retries: int = 3):
    """Combined decorator for rate limiting, retry, and error handling"""
    def decorator(func: Callable) -> Callable:
        # Apply decorators in reverse order (innermost first)
        decorated = with_error_handling(endpoint_type)(func)
        decorated = with_retry(max_retries)(decorated)
        decorated = with_rate_limiting(endpoint_type)(decorated)
        return decorated
    return decorator


# Usage example:
# @api_call(endpoint_type="order_submit", max_retries=2)
# def submit_order(...):
#     # Your API call here
#     pass
