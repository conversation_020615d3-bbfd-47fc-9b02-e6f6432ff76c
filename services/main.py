import json
import logging
import sys
import time
from contextvars import ContextVar
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Dict

from fastapi import FastAPI, Request, HTTPException
from pybit.unified_trading import HTTP

"""
🔧 Bybit FastAPI Service Management (on remote server):

# SSH into the server:
ssh root@128.199.252.188

# 🚀 Upload updated main.py from local to server:
scp "PycharmProjects/Journal/services/main.py" root@128.199.252.188:/home/<USER>/
scp "PycharmProjects/CDOS/services/main.py" root@128.199.252.188:/home/<USER>/

# 🔁 Restart/Stop the FastAPI service:
sudo systemctl restart fastapi-bybit
sudo systemctl stop fastapi-bybit

# ✅ Check service status:
sudo systemctl status fastapi-bybit

# 📄 View logs (tail):
journalctl -u fastapi-bybit -f

# 📄 View last 100 log lines:
journalctl -u fastapi-bybit -n 100

# # JAPAN FIRST BEFORE GOING HERE # #
https://www.bybit.com/en/help-center/article/Introduction-to-Take-Profit-Stop-Loss-Perpetual-Futures-Contracts
"""

API_TOKEN = "9x!W#2@f^P7zRk8sQyL0dTgVh3mZcB1nUoE*XaJ4lM"

app = FastAPI()
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)  # send to stdout (journalctl picks this up)
    ]
)

logger = logging.getLogger("bybit")  # optional: name your logger
logger.info("✅ Logging is now set up and should appear in journalctl")


def _parse_credentials(request: Request):
    api_key = request.headers.get("x-bybit-api-key")
    api_secret = request.headers.get("x-bybit-api-secret")
    testnet_header = request.headers.get("x-bybit-testnet", "false")
    testnet = str(testnet_header).lower() in ("1", "true", "yes", "on")
    if not api_key or not api_secret:
        return None
    return {"api_key": api_key, "api_secret": api_secret, "testnet": testnet}

credentials_ctx = ContextVar("bybit_credentials", default=None)

def get_session():
    """Create pybit HTTP session using per-request credentials"""
    creds = credentials_ctx.get()
    if not creds:
        raise HTTPException(
            status_code=400,
            detail="Missing Bybit credentials. Provide x-bybit-api-key and x-bybit-api-secret headers."
        )
    logger.info(f"Creating new session for testnet={creds['testnet']}, api_key={creds['api_key']}, api_secret={creds['api_secret']}")
    return HTTP(testnet=creds["testnet"], api_key=creds["api_key"], api_secret=creds["api_secret"])


@app.middleware("http")
async def verify_token(request: Request, call_next):
    if request.headers.get("x-api-token") != API_TOKEN:
        raise HTTPException(status_code=403, detail="Invalid token")
    # Store Bybit credentials for this request context (if provided)
    logger.info(f"Store Bybit credentials for request: {request.url}")
    creds = _parse_credentials(request)
    if creds:
        credentials_ctx.set(creds)
    return await call_next(request)


@app.get("/debug")
def debug():
    logger.info("✅ This should be in journalctl")
    return {"ok": True}




@app.get("/bybit/balance")
def get_balance():
    result = get_session().get_wallet_balance(accountType="UNIFIED", coin="USDT")
    return {"equity": float(result["result"]["list"][0]["totalEquity"])}


@app.get("/bybit/executions")
def get_executions():
    result = get_session().get_executions(category="linear", limit=30)
    return result["result"]["list"]


@app.get("/bybit/price")
def get_price(ticker: str):
    result = get_session().get_kline(symbol=ticker, interval=1, limit=30)
    return float(result["result"]["list"][0][4])


@app.get("/bybit/orders")
def get_orders(startTime: Optional[int] = None, endTime: Optional[int] = None):
    session = get_session()
    orders_list = []

    # If no startTime provided, default to 2 years ago
    if not startTime:
        start_dt = datetime.utcnow() - timedelta(days=730)
    else:
        start_dt = datetime.utcfromtimestamp(startTime / 1000)

    # If no endTime provided, default to now
    if not endTime:
        end_dt = datetime.utcnow()
    else:
        end_dt = datetime.utcfromtimestamp(endTime / 1000)

    start_ms = int(start_dt.timestamp() * 1000)
    end_ms = int(end_dt.timestamp() * 1000)

    logger.info(f"🔍 Starting order fetch from {start_dt} to {end_dt}")
    logger.info(f"🧮 Start MS: {start_ms}, End MS: {end_ms}")

    while start_ms < end_ms:
        window_end_ms = min(start_ms + (7 * 24 * 60 * 60 * 1000), end_ms)

        logger.info(f"\n📅 Fetching window:")
        logger.info(f"   Start: {datetime.utcfromtimestamp(start_ms / 1000)} ({start_ms})")
        logger.info(f"   End:   {datetime.utcfromtimestamp(window_end_ms / 1000)} ({window_end_ms})")
        delta_ms = window_end_ms - start_ms
        logger.info(f"⏱ Window size: {delta_ms} ms ({delta_ms / 1000:.2f} seconds)")

        nxtPageCur = None
        page_count = 0
        total_in_window = 0

        while True:
            kwargs = {
                "category": "linear",
                "limit": 50,
                "startTime": start_ms,
                "endTime": window_end_ms
            }
            if nxtPageCur:
                kwargs["cursor"] = nxtPageCur

            response = session.get_order_history(**kwargs)
            result = response["result"]
            page_orders = result.get("list", [])
            orders_list += page_orders
            total_in_window += len(page_orders)
            nxtPageCur = result.get("nextPageCursor")
            page_count += 1

            logger.info(f"   🔄 Page {page_count}: {len(page_orders)} orders")

            if not nxtPageCur or page_count > 30:
                break

            time.sleep(0.1)

        logger.info(f"✅ Finished window: {total_in_window} orders across {page_count} page(s)")

        start_ms = window_end_ms + 1  # move forward 7 days

    logger.info(f"\n🎯 Total orders fetched: {len(orders_list)}")
    return orders_list


@app.get("/bybit/positions")
def get_open_positions(category: str = "linear", settleCoin: Optional[str] = None):
    """
    Get open positions from Bybit.

    Args:
        category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
        settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.

    Returns:
        list: A list of open positions
    """
    session = get_session()

    # Prepare parameters for the API call
    params = {"category": category}
    if settleCoin:
        params["settleCoin"] = settleCoin

    # Make the API call with the parameters
    response = session.get_positions(**params)
    logger.info(response)
    # Filter out closed positions (size = 0)
    open_positions = [p for p in response["result"]["list"] if float(p["size"]) > 0]

    logger.info(f"Retrieved {len(open_positions)} open positions for category={category}" +
                (f", settleCoin={settleCoin}" if settleCoin else ""))

    return open_positions


@app.get("/bybit/kline")
def bybit_kline(symbol: str, interval: str = "60", limit: int = 500,
                start: int | None = None, end: int | None = None):
    # Use public V5 endpoint directly (no auth) from Japan
    import requests
    url = "https://api.bybit.com/v5/market/kline"
    params = {"category": "linear", "symbol": symbol, "interval": interval, "limit": min(max(1, limit), 1000)}
    if start is not None: params["start"] = int(start)
    if end is not None: params["end"] = int(end)
    r = requests.get(url, params=params, timeout=10)
    r.raise_for_status()
    return r.json()


@app.get("/bybit/instruments")
def bybit_instruments():
    import requests
    url = "https://api.bybit.com/v5/market/instruments-info"
    params = {"category": "linear"}
    r = requests.get(url, params=params, timeout=10)
    r.raise_for_status()
    return r.json()


@app.get("/bybit/instruments-info")
def get_instruments_info(
    category: str,
    symbol: Optional[str] = None,
    status: Optional[str] = None,
    baseCoin: Optional[str] = None,
    limit: Optional[int] = None,
    cursor: Optional[str] = None
):
    """
    Get instruments info from Bybit.

    Args:
        category: Product type ("spot", "linear", "inverse", "option")
        symbol: Trading pair symbol (optional)
        status: Instrument status filter (optional)
        baseCoin: Base coin filter (optional)
        limit: Number of results, max 1000 (optional)
        cursor: Pagination cursor (optional)

    Returns:
        dict: Instruments info response from Bybit
    """
    # Use public V5 endpoint directly (no auth needed) from Japan
    import requests
    url = "https://api.bybit.com/v5/market/instruments-info"
    params = {"category": category}
    
    if symbol:
        params["symbol"] = symbol
    if status:
        params["status"] = status
    if baseCoin:
        params["baseCoin"] = baseCoin
    if limit:
        params["limit"] = min(max(1, limit), 1000)
    if cursor:
        params["cursor"] = cursor

    logger.info(f"🔍 Getting instruments info for category={category}, params={params}")

    try:
        r = requests.get(url, params=params, timeout=10)
        r.raise_for_status()
        
        response = r.json()
        result_list = response.get("result", {}).get("list", [])
        logger.info(f"✅ Retrieved {len(result_list)} instruments for category={category}")
        
        return response

    except Exception as e:
        logger.error(f"❌ Error getting instruments info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get instruments info: {str(e)}")


# ========================================
# ORDER MANAGEMENT ENDPOINTS
# ========================================

from pydantic import BaseModel
from typing import Optional, Literal


class OrderRequest(BaseModel):
    """Request model for placing orders"""
    symbol: str
    side: Literal["Buy", "Sell"]
    orderType: Literal["Market", "Limit"]
    qty: str  # String to preserve precision
    price: Optional[str] = None  # Required for Limit orders
    timeInForce: Optional[Literal["GTC", "IOC", "FOK"]] = "GTC"
    reduceOnly: Optional[bool] = False
    closeOnTrigger: Optional[bool] = False
    orderLinkId: Optional[str] = None  # Client order ID
    category: str = "linear"


class StopOrderRequest(BaseModel):
    """Request model for placing stop/take-profit orders"""
    symbol: str
    side: Literal["Buy", "Sell"]
    orderType: Literal["Stop", "StopLimit", "TakeProfit", "TakeProfitLimit"]
    qty: str
    price: Optional[str] = None  # For StopLimit/TakeProfitLimit
    triggerPrice: str  # Stop/TP trigger price
    triggerDirection: int  # 1 for Rising, 2 for Falling
    timeInForce: Optional[Literal["GTC", "IOC", "FOK"]] = "GTC"
    reduceOnly: Optional[bool] = False
    closeOnTrigger: Optional[bool] = False
    orderLinkId: Optional[str] = None
    category: str = "linear"


class OrderUpdateRequest(BaseModel):
    """Request model for updating orders"""
    orderId: Optional[str] = None
    orderLinkId: Optional[str] = None
    symbol: str
    qty: Optional[str] = None
    price: Optional[str] = None
    triggerPrice: Optional[str] = None
    category: str = "linear"


class OrderCancelRequest(BaseModel):
    """Request model for canceling orders"""
    orderId: Optional[str] = None
    orderLinkId: Optional[str] = None
    symbol: str
    category: str = "linear"


@app.post("/bybit/order/submit")
def submit_order(order_req: OrderRequest):
    """Submit a new order (Market or Limit)"""
    try:
        session = get_session()

        # Prepare order parameters
        params = {
            "category": order_req.category,
            "symbol": order_req.symbol,
            "side": order_req.side,
            "orderType": order_req.orderType,
            "qty": order_req.qty,
            "timeInForce": order_req.timeInForce,
            "reduceOnly": order_req.reduceOnly,
            "closeOnTrigger": order_req.closeOnTrigger,
            "positionIdx": 1 if order_req.side == "Buy" else 2  # Hedge mode: 1=Long, 2=Short
        }

        # Add price for Limit orders
        if order_req.orderType == "Limit":
            if not order_req.price:
                raise HTTPException(status_code=400, detail="Price is required for Limit orders")
            params["price"] = order_req.price

        # Add optional client order ID
        if order_req.orderLinkId:
            params["orderLinkId"] = order_req.orderLinkId

        logger.info(f"🚀 Submitting order: {params}")

        # Submit order via pybit
        response = session.place_order(**params)

        logger.info(f"✅ Order submitted successfully: {response}")
        return response

    except Exception as e:
        logger.error(f"❌ Error submitting order: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Order submission failed: {str(e)}")


@app.post("/bybit/order/submit-stop")
def submit_stop_order(order_req: StopOrderRequest):
    """Submit a stop-loss or take-profit order"""
    try:
        session = get_session()

        # Map orderType to stopOrderType
        stop_order_type_mapping = {
            "Stop": "StopLoss",
            "TakeProfit": "TakeProfit",
            "TrailingStop": "TrailingStop"
        }

        # Prepare order parameters
        params = {
            "category": order_req.category,
            "symbol": order_req.symbol,
            "side": order_req.side,
            "orderType": "Market",
            "stopOrderType": stop_order_type_mapping.get(order_req.orderType, order_req.orderType),
            "qty": order_req.qty,
            "triggerPrice": order_req.triggerPrice,
            "triggerDirection": order_req.triggerDirection,
            "timeInForce": order_req.timeInForce,
            "reduceOnly": order_req.reduceOnly,
            "closeOnTrigger": order_req.closeOnTrigger,
            "positionIdx": 1 if order_req.side == "Buy" else 2  # Hedge mode: 1=Long, 2=Short
        }

        # Add optional client order ID
        if order_req.orderLinkId:
            params["orderLinkId"] = order_req.orderLinkId

        logger.info(f"🛑 Submitting stop order: {params}")

        # Submit order via pybit
        response = session.place_order(**params)

        logger.info(f"✅ Stop order submitted successfully: {response}")
        return response

    except Exception as e:
        logger.error(f"❌ Error submitting stop order: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Stop order submission failed: {str(e)}")


@app.get("/bybit/order/status")
def get_order_status(orderId: Optional[str] = None, orderLinkId: Optional[str] = None,
                     symbol: Optional[str] = None, category: str = "linear"):
    """Get order status by order ID or client order ID"""
    try:
        if not orderId and not orderLinkId:
            raise HTTPException(status_code=400, detail="Either orderId or orderLinkId must be provided")

        session = get_session()

        params = {"category": category}
        if orderId:
            params["orderId"] = orderId
        if orderLinkId:
            params["orderLinkId"] = orderLinkId
        if symbol:
            params["symbol"] = symbol

        logger.info(f"🔍 Getting order status: {params}")

        response = session.get_open_orders(**params)

        logger.info(f"✅ Order status retrieved: {response}")
        return response

    except Exception as e:
        logger.error(f"❌ Error getting order status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get order status: {str(e)}")


@app.get("/bybit/orders/active")
def get_active_orders(symbol: Optional[str] = None, category: str = "linear", limit: int = 50):
    """Get all active orders"""
    try:
        session = get_session()

        params = {
            "category": category,
            "limit": limit
        }
        if symbol:
            params["symbol"] = symbol

        logger.info(f"📋 Getting active orders: {params}")

        response = session.get_open_orders(**params)

        logger.info(f"✅ Active orders retrieved: {len(response.get('result', {}).get('list', []))} orders")
        return response

    except Exception as e:
        logger.error(f"❌ Error getting active orders: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get active orders: {str(e)}")


@app.post("/bybit/order/update")
def update_order(order_req: OrderUpdateRequest):
    """Update an existing order"""
    try:
        if not order_req.orderId and not order_req.orderLinkId:
            raise HTTPException(status_code=400, detail="Either orderId or orderLinkId must be provided")

        session = get_session()

        # Prepare update parameters
        params = {
            "category": order_req.category,
            "symbol": order_req.symbol
        }

        if order_req.orderId:
            params["orderId"] = order_req.orderId
        if order_req.orderLinkId:
            params["orderLinkId"] = order_req.orderLinkId
        if order_req.qty:
            params["qty"] = order_req.qty
        if order_req.price:
            params["price"] = order_req.price
        if order_req.triggerPrice:
            params["triggerPrice"] = order_req.triggerPrice

        logger.info(f"✏️ Updating order: {params}")

        response = session.amend_order(**params)

        logger.info(f"✅ Order updated successfully: {response}")
        return response

    except Exception as e:
        logger.error(f"❌ Error updating order: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Order update failed: {str(e)}")


@app.post("/bybit/order/cancel")
def cancel_order(order_req: OrderCancelRequest):
    """Cancel a specific order"""
    try:
        if not order_req.orderId and not order_req.orderLinkId:
            raise HTTPException(status_code=400, detail="Either orderId or orderLinkId must be provided")

        session = get_session()

        params = {
            "category": order_req.category,
            "symbol": order_req.symbol
        }

        if order_req.orderId:
            params["orderId"] = order_req.orderId
        if order_req.orderLinkId:
            params["orderLinkId"] = order_req.orderLinkId

        logger.info(f"🗑️ Canceling order: {params}")

        response = session.cancel_order(**params)

        logger.info(f"✅ Order canceled successfully: {response}")
        return response

    except Exception as e:
        logger.error(f"❌ Error canceling order: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Order cancellation failed: {str(e)}")


@app.post("/bybit/orders/cancel-all")
def cancel_all_orders(symbol: Optional[str] = None, category: str = "linear"):
    """Cancel all orders for a symbol or all symbols"""
    try:
        session = get_session()

        params = {"category": category}
        if symbol:
            params["symbol"] = symbol

        logger.info(f"🗑️🗑️ Canceling all orders: {params}")

        response = session.cancel_all_orders(**params)

        logger.info(f"✅ All orders canceled successfully: {response}")
        return response

    except Exception as e:
        logger.error(f"❌ Error canceling all orders: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Cancel all orders failed: {str(e)}")


@app.get("/bybit/orders/history")
def get_order_history(symbol: Optional[str] = None, category: str = "linear",
                      limit: int = 50, cursor: Optional[str] = None):
    """Get order history with pagination"""
    try:
        session = get_session()

        params = {
            "category": category,
            "limit": limit
        }
        if symbol:
            params["symbol"] = symbol
        if cursor:
            params["cursor"] = cursor

        logger.info(f"📚 Getting order history: {params}")

        response = session.get_order_history(**params)

        logger.info(f"✅ Order history retrieved: {len(response.get('result', {}).get('list', []))} orders")
        return response

    except Exception as e:
        logger.error(f"❌ Error getting order history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get order history: {str(e)}")


# ========================================
# TRADE MANAGEMENT ENDPOINTS
# ========================================

class BracketOrderRequest(BaseModel):
    """Request model for bracket orders (entry + stop-loss + take-profit)"""
    symbol: str
    side: Literal["Buy", "Sell"]
    qty: str
    entryPrice: Optional[str] = None  # None for market entry
    stopLossPrice: str
    takeProfitPrice: str
    orderLinkId: Optional[str] = None
    category: str = "linear"


@app.post("/bybit/order/bracket")
def submit_bracket_order(bracket_req: BracketOrderRequest):
    """Submit a bracket order (entry + stop-loss + take-profit)"""
    try:
        session = get_session()
        results = []

        # Generate unique order link IDs if not provided
        base_link_id = bracket_req.orderLinkId or f"bracket_{int(time.time())}"

        # 1. Submit entry order
        entry_params = {
            "category": bracket_req.category,
            "symbol": bracket_req.symbol,
            "side": bracket_req.side,
            "qty": bracket_req.qty,
            "orderLinkId": f"{base_link_id}_entry",
            "positionIdx": 1 if bracket_req.side == "Buy" else 2  # Hedge mode: 1=Long, 2=Short
        }

        if bracket_req.entryPrice:
            entry_params["orderType"] = "Limit"
            entry_params["price"] = bracket_req.entryPrice
            entry_params["timeInForce"] = "GTC"
        else:
            entry_params["orderType"] = "Market"

        logger.info(f"🎯 Submitting bracket entry order: {entry_params}")
        entry_response = session.place_order(**entry_params)
        results.append({"type": "entry", "response": entry_response})

        # 2. Submit stop-loss order
        stop_side = "Buy" if bracket_req.side == "Sell" else "Sell"
        
        # For stop-loss: Long positions trigger on falling price, Short positions trigger on rising price
        if bracket_req.side == "Buy":  # Long position
            stop_trigger_direction = 2  # Falling - trigger when price drops below stop
        else:  # Short position
            stop_trigger_direction = 1  # Rising - trigger when price rises above stop

        stop_params = {
            "category": bracket_req.category,
            "symbol": bracket_req.symbol,
            "side": stop_side,
            "orderType": "Market",
            "stopOrderType": "StopLoss",
            "qty": bracket_req.qty,
            "triggerPrice": bracket_req.stopLossPrice,
            "triggerDirection": stop_trigger_direction,
            "reduceOnly": True,
            "orderLinkId": f"{base_link_id}_sl",
            "positionIdx": 1 if bracket_req.side == "Buy" else 2  # Match entry position
        }

        logger.info(f"🛑 Submitting bracket stop-loss order: {stop_params}")
        stop_response = session.place_order(**stop_params)
        results.append({"type": "stop_loss", "response": stop_response})

        # 3. Submit take-profit order
        tp_side = "Buy" if bracket_req.side == "Sell" else "Sell"
        
        # For take-profit: Long positions trigger on rising price, Short positions trigger on falling price
        if bracket_req.side == "Buy":  # Long position
            tp_trigger_direction = 1  # Rising - trigger when price rises above TP
        else:  # Short position
            tp_trigger_direction = 2  # Falling - trigger when price falls below TP

        tp_params = {
            "category": bracket_req.category,
            "symbol": bracket_req.symbol,
            "side": tp_side,
            "orderType": "Market",
            "stopOrderType": "TakeProfit",
            "qty": bracket_req.qty,
            "triggerPrice": bracket_req.takeProfitPrice,
            "triggerDirection": tp_trigger_direction,
            "reduceOnly": True,
            "orderLinkId": f"{base_link_id}_tp",
            "positionIdx": 1 if bracket_req.side == "Buy" else 2  # Match entry position
        }

        logger.info(f"🎯 Submitting bracket take-profit order: {tp_params}")
        tp_response = session.place_order(**tp_params)
        results.append({"type": "take_profit", "response": tp_response})

        logger.info(f"✅ Bracket order submitted successfully: {len(results)} orders")
        return {
            "success": True,
            "bracket_id": base_link_id,
            "orders": results
        }

    except Exception as e:
        logger.error(f"❌ Error submitting bracket order: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Bracket order submission failed: {str(e)}")


# ========================================
# REAL-TIME UPDATES VIA SERVER-SENT EVENTS
# ========================================

from fastapi.responses import StreamingResponse
from collections import deque
from dataclasses import dataclass, field
import secrets

# ============================
# Per-session streaming manager
# ============================
@dataclass
class Session:
    api_key: str
    api_secret: str
    testnet: bool = False
    queue: deque = field(default_factory=lambda: deque(maxlen=1000))
    monitoring_orders: set = field(default_factory=set)
    active: bool = False
    thread: Optional[object] = None  # background thread handle

# session_id -> Session
sessions: Dict[str, Session] = {}


def add_update_message(session_id: str, message_type: str, data: dict):
    """Add a message to a session's update queue"""
    sess = sessions.get(session_id)
    if not sess or not sess.active:
        return
    message = {
        "type": message_type,
        "data": data,
        "timestamp": time.time(),
        "session_id": session_id,
    }
    sess.queue.append(message)


@app.get("/bybit/stream/orders")
async def stream_order_updates(session_id: str):
    """Stream real-time order updates for a specific session via Server-Sent Events"""
    sess = sessions.get(session_id)
    if not sess or not sess.active:
        raise HTTPException(status_code=404, detail="Invalid or inactive session_id")

    async def event_stream():
        # Initial event
        yield f"event: connected\ndata: {json.dumps({'timestamp': time.time(), 'session_id': session_id})}\n\n"
        try:
            while True:
                if sess.queue:
                    msg = sess.queue.popleft()
                    yield f"data: {json.dumps(msg)}\n\n"
                else:
                    await asyncio.sleep(0.25)
        except asyncio.CancelledError:
            logger.info(f"📡 SSE connection closed for session {session_id}")

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@app.get("/bybit/stream/status")
def get_stream_status(session_id: Optional[str] = None):
    """Get streaming status (global or per session)"""
    if session_id:
        sess = sessions.get(session_id)
        if not sess:
            raise HTTPException(status_code=404, detail="Unknown session_id")
        return {
            "session_id": session_id,
            "active": sess.active,
            "queued_messages": len(sess.queue),
            "monitored_orders": list(sess.monitoring_orders),
        }
    return {
        "sessions": {sid: {"active": s.active, "queued_messages": len(s.queue)} for sid, s in sessions.items()}
    }


# ========================================
# WEBSOCKET-BASED REAL-TIME MONITORING
# ========================================

import threading
import asyncio
import websockets
import hmac
import hashlib
from typing import Optional

# Per-session WebSocket handler uses Session creds
class BybitWebSocketHandler:
    """WebSocket handler for Bybit real-time updates on Japan server (per session)"""

    def __init__(self, session_id: str, session: Session):
        self.session_id = session_id
        self.session = session
        self.api_key = session.api_key
        self.api_secret = session.api_secret
        self.testnet = session.testnet
        self.websocket = None
        self.is_connected = False
        self.is_authenticated = False


    def get_websocket_url(self):
        """Get WebSocket URL based on environment"""
        if self.testnet:
            return "wss://stream-testnet.bybit.com/v5/private"
        else:
            return "wss://stream.bybit.com/v5/private"

    def generate_signature(self, expires: int) -> str:
        """Generate authentication signature"""
        param_str = f"GET/realtime{expires}"
        return hmac.new(
            self.api_secret.encode('utf-8'),
            param_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def authenticate(self):
        """Authenticate WebSocket connection"""
        expires = int((time.time() + 10) * 1000)
        signature = self.generate_signature(expires)

        auth_message = {
            "op": "auth",
            "args": [self.api_key, expires, signature]
        }

        await self.websocket.send(json.dumps(auth_message))
        logger.info("🔐 WebSocket authentication sent")

    async def subscribe_to_topics(self):
        """Subscribe to order and position updates"""
        subscribe_message = {
            "op": "subscribe",
            "args": ["order", "position", "execution"]
        }

        await self.websocket.send(json.dumps(subscribe_message))
        logger.info("📡 Subscribed to order, position, and execution updates")

    async def handle_message(self, message: str):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)

            # Handle authentication response
            if data.get("op") == "auth":
                if data.get("success"):
                    self.is_authenticated = True
                    logger.info("✅ WebSocket authenticated successfully")
                    await self.subscribe_to_topics()
                else:
                    logger.error("❌ WebSocket authentication failed")
                    return

            # Handle subscription response
            elif data.get("op") == "subscribe":
                if data.get("success"):
                    logger.info(f"✅ Successfully subscribed to: {data.get('args', [])}")
                else:
                    logger.error(f"❌ Subscription failed: {data}")

            # Handle data updates
            elif data.get("topic"):
                await self.process_update(data)

        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse WebSocket message: {e}")
        except Exception as e:
            logger.error(f"❌ Error handling WebSocket message: {e}")

    async def process_update(self, data: dict):
        """Process real-time updates from Bybit"""
        topic = data.get("topic", "")
        message_data = data.get("data", [])

        # Process order updates
        if topic == "order":
            for order_data in message_data:
                order_id = order_data.get("orderId")

                # Only send updates for orders we're monitoring
                if not self.session.monitoring_orders or order_id in self.session.monitoring_orders:
                    # Send immediate update via SSE
                    add_update_message(self.session_id, "order_update", {
                        "orderId": order_id,
                        "symbol": order_data.get("symbol"),
                        "orderStatus": order_data.get("orderStatus"),
                        "cumExecQty": order_data.get("cumExecQty"),
                        "leavesQty": order_data.get("leavesQty"),
                        "avgPrice": order_data.get("avgPrice"),
                        "side": order_data.get("side"),
                        "orderType": order_data.get("orderType"),
                        "source": "websocket"  # Mark as real-time
                    })

                    logger.info(f"⚡ Real-time order update: {order_id} -> {order_data.get('orderStatus')}")

                    # Remove completed orders from monitoring
                    if order_data.get("orderStatus") in ["Filled", "Cancelled", "Rejected"]:
                        self.session.monitoring_orders.discard(order_id)

        # Process execution updates
        elif topic == "execution":
            for exec_data in message_data:
                order_id = exec_data.get("orderId")

                if not self.session.monitoring_orders or order_id in self.session.monitoring_orders:
                    add_update_message(self.session_id, "execution_update", {
                        "orderId": order_id,
                        "symbol": exec_data.get("symbol"),
                        "execQty": exec_data.get("execQty"),
                        "execPrice": exec_data.get("execPrice"),
                        "execTime": exec_data.get("execTime"),
                        "side": exec_data.get("side"),
                        "source": "websocket"
                    })

                    logger.info(f"⚡ Real-time execution: {order_id} - {exec_data.get('execQty')} @ {exec_data.get('execPrice')}")

    async def connect_and_run(self):
        """Connect to WebSocket and handle messages for this session"""
        ws_url = self.get_websocket_url()

        try:
            logger.info(f"🔌 Connecting to Bybit WebSocket: {ws_url}")

            async with websockets.connect(ws_url) as websocket:
                self.websocket = websocket
                self.is_connected = True

                # Authenticate
                await self.authenticate()

                # Listen for messages
                async for message in websocket:
                    if not self.session.active:
                        break
                    await self.handle_message(message)

        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ WebSocket connection closed")
        except Exception as e:
            logger.error(f"❌ WebSocket error: {e}")
        finally:
            self.is_connected = False
            self.is_authenticated = False


def run_websocket_loop(session_id: str):
    """Run WebSocket in asyncio event loop for a specific session"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    sess = sessions.get(session_id)
    if not sess:
        logger.error(f"❌ Unknown session_id: {session_id}")
        return

    handler = BybitWebSocketHandler(session_id, sess)
    sess.active = True

    while sess.active:
        try:
            loop.run_until_complete(handler.connect_and_run())
        except Exception as e:
            logger.error(f"❌ WebSocket loop error: {e}")
        if sess.active:
            logger.info("🔄 Reconnecting WebSocket in 5 seconds...")
            time.sleep(5)

    loop.close()
    logger.info(f"🛑 WebSocket loop stopped for session {session_id}")


@app.post("/bybit/monitor/start")
async def start_order_monitoring(request: Request):
    """Start a session-specific WebSocket order monitoring connection.
    Body JSON: { api_key, api_secret, testnet?: bool, order_ids?: [str] }
    """
    try:
        body = await request.json()
    except Exception:
        body = {}

    api_key = body.get("api_key") or request.headers.get("x-bybit-api-key")
    api_secret = body.get("api_secret") or request.headers.get("x-bybit-api-secret")
    testnet = bool(body.get("testnet", str(request.headers.get("x-bybit-testnet", "false")).lower() in ("1","true","yes","on")))
    order_ids = body.get("order_ids") or []

    if not api_key or not api_secret:
        raise HTTPException(status_code=400, detail="Missing api_key/api_secret for websocket session")

    session_id = secrets.token_hex(12)
    sess = Session(api_key=api_key, api_secret=api_secret, testnet=testnet, active=True)
    sess.monitoring_orders.update(order_ids)
    sessions[session_id] = sess

    # Start background WS thread for this session
    t = threading.Thread(target=run_websocket_loop, args=(session_id,), daemon=True)
    sess.thread = t
    t.start()

    logger.info(f"🚀 Started WebSocket order monitoring for session {session_id}")

    return {
        "session_id": session_id,
        "websocket_active": True,
        "monitored_orders": list(sess.monitoring_orders),
        "message": "WebSocket order monitoring started",
        "real_time": True,
    }


@app.post("/bybit/monitor/stop")
async def stop_order_monitoring(request: Request, session_id: Optional[str] = None):
    """Stop a specific WebSocket monitoring session"""
    if not session_id:
        try:
            body = await request.json()
            session_id = body.get("session_id")
        except Exception:
            session_id = None
    if not session_id or session_id not in sessions:
        raise HTTPException(status_code=404, detail="Unknown session_id")

    sess = sessions[session_id]
    sess.active = False
    # Optionally wait a short time for thread to exit
    t = getattr(sess, "thread", None)
    if t is not None:
        try:
            import threading as _threading
            if isinstance(t, _threading.Thread):
                if t.is_alive():
                    t.join(timeout=1.0)
            else:
                # Fallback: handle objects with similar interface
                is_alive = getattr(t, "is_alive", None)
                join = getattr(t, "join", None)
                if callable(is_alive) and is_alive() and callable(join):
                    join(timeout=1.0)
        except Exception as e:
            logger.warning(f"Failed to join session thread: {e}")

    del sessions[session_id]
    logger.info(f"🛑 Stopped WebSocket session {session_id}")

    return {"websocket_active": False, "message": "WebSocket session stopped", "session_id": session_id}



@app.get("/bybit/monitor/status")
def get_monitoring_status(session_id: Optional[str] = None):
    """Get session-aware WebSocket monitoring status"""
    if session_id:
        sess = sessions.get(session_id)
        if not sess:
            raise HTTPException(status_code=404, detail="Unknown session_id")
        return {
            "session_id": session_id,
            "active": sess.active,
            "monitored_orders": list(sess.monitoring_orders),
            "queued_messages": len(sess.queue),
        }
    return {
        "sessions": {
            sid: {
                "active": s.active,
                "monitored_orders": len(s.monitoring_orders),
                "queued_messages": len(s.queue),
            }
            for sid, s in sessions.items()
        }
    }
