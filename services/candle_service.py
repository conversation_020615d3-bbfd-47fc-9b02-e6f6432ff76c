from __future__ import annotations

from typing import Dict, Optional
import pandas as pd

from services.exchange_interface import ExchangeProvider, TimeFrame
from services.providers.binance_provider import BinanceProvider
from services.providers.bybit_provider import BybitProvider


class CandleService:
    def __init__(self):
        # Local service only manages Binance directly; Bybit is accessed via services.bybit proxy
        self.providers: Dict[str, ExchangeProvider] = {
            'binance': BinanceProvider(),
            'bybit': BybitProvider(),
        }

    def get_candles(self, exchange: str, symbol: str, timeframe: str,
                    limit: int = 500, start_ms: Optional[int] = None,
                    end_ms: Optional[int] = None) -> pd.DataFrame:
        provider = self.providers.get(exchange)
        if not provider:
            raise ValueError(f"Unsupported exchange: {exchange}")
        tf = TimeFrame.from_str(timeframe)
        return provider.get_candles(symbol=symbol, timeframe=tf, limit=limit,
                                    start_ms=start_ms, end_ms=end_ms)

    def get_historical_candles(self, exchange: str, symbol: str, timeframe: str,
                               before_ms: int, limit: int = 500) -> pd.DataFrame:
        # Fetch candles ending before 'before_ms'
        return self.get_candles(exchange, symbol, timeframe, limit=limit,
                                end_ms=before_ms)

    def get_symbols(self, exchange: str):
        provider = self.providers.get(exchange)
        if not provider:
            raise ValueError(f"Unsupported exchange: {exchange}")
        return provider.get_symbols()

