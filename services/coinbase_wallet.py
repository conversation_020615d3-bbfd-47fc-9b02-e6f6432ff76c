import json
import os
import subprocess
from decimal import Decimal, ROUND_HALF_UP

from coinbase_advanced import jwt_generator
from models.coinbase_fill import Fill
from models.coinbase_order import CoinbaseOrder

# Load API keys from JournalKeys2.json
desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
json_file_path = os.path.join(desktop_path, "JournalKeys2.json")

try:
    with open(json_file_path, "r") as file:
        keys = json.load(file)
except FileNotFoundError:
    raise RuntimeError("Error: API keys file not found.")
except json.JSONDecodeError:
    raise RuntimeError("Error: Malformed JSON file.")

BASE_URL = "https://api.coinbase.com"


def generate_jwt(request_method, original_request_path):
    """Generate a JWT using only the original request path, ignoring pagination."""
    jwt_uri = jwt_generator.format_jwt_uri(request_method, original_request_path)
    return jwt_generator.build_rest_jwt(jwt_uri, keys["name"], keys["privateKey"])


def fetch_paginated_data(request_method, request_path):
    """Fetch paginated data while keeping JWT signing consistent with the original request path."""
    all_data = []
    original_request_path = request_path  # Preserve the original path for JWT

    while request_path:
        jwt_token = generate_jwt(request_method, original_request_path)  # Always sign with original path

        curl_command = f'curl -H "Authorization: Bearer {jwt_token}" "{BASE_URL}{request_path}"'
        process = subprocess.run(curl_command, shell=True, capture_output=True, text=True)

        try:
            response_json = json.loads(process.stdout)
        except json.JSONDecodeError:
            raise RuntimeError(f"Failed to parse JSON response: {process.stdout}")

        # Append the retrieved data
        all_data.extend(response_json.get("data", []))

        # Get next page URI (but keep using original path for JWT)
        request_path = response_json.get("pagination", {}).get("next_uri")

    return all_data


def get_xlm_account_id():
    """Finds the XLM account ID using paginated requests."""
    accounts = fetch_paginated_data("GET", "/v2/accounts")

    for account in accounts:
        if account.get("currency").get("code") == "XLM":
            return account.get("id")

    raise ValueError("XLM account not found.")


def get_transactions(account_id):
    """Fetches all transactions for the given account ID using pagination and creates CoinbaseOrder objects."""
    transactions = fetch_paginated_data("GET", f"/v2/accounts/{account_id}/transactions")

    print("Transactions:")
    coinbase_orders = []
    for tx in transactions:
        print(
            f"- Transaction ID: {tx['id']}, Amount: {tx['amount']['amount']} {tx['amount']['currency']}, Status: {tx['status']}")

        unique_statuses = {transaction["type"] for transaction in transactions}

        # Convert back to a list (optional)
        unique_statuses_list = list(unique_statuses)

        print(unique_statuses_list)  # Output: ['pending', 'completed', 'failed']

        # Create a CoinbaseOrder object
        coinbase_order = CoinbaseOrder(Fill(
            commission=get_commission(tx),
            entry_id=None,
            liquidity_indicator=None,  # Not in CSV
            price=get_price(tx),
            order_id=tx.get('id'),
            product_id=get_product_id(tx),
            retail_portfolio_id=None,  # Not in CSV
            sequence_timestamp=tx.get('created_at'),
            side=determine_side(tx.get('type')),
            size=str(abs(Decimal(tx.get('amount', {}).get('amount', 0)))) if tx.get('amount') else 0,
            size_in_quote=False,  # Defaulting to False
            trade_id=get_trade_id(tx),  # Assuming ID is the trade ID
            trade_time=tx.get('created_at'),
            trade_type=tx.get('type'),  # Not in CSV
            user_id=None,  # Not in CSV
            notes=None
        ))
        coinbase_orders.append(coinbase_order)

    return coinbase_orders


def determine_side(trans_type: str) -> str | None:
    if trans_type == "Receive":
        return "buy"
    elif trans_type == "advanced_trade_fill":
        return "buy"
    elif trans_type == "send":
        return "sell"
    elif trans_type == "Convert":
        return "sell"
    elif trans_type == "pro_withdrawal":
        return "sell"
    elif trans_type == "pro_deposit":
        return "sell"
    elif trans_type == "advanced_trade_sell":
        return "sell"


def get_price(tx):
    if tx.get('advanced_trade_fill'):
        return tx.get('advanced_trade_fill').get('fill_price')
    elif tx.get('amount') and tx.get('native_amount'):
        coin_qty_amount = abs(Decimal(tx.get('amount').get('amount')))

        sell_fee = Decimal(0)
        if tx.get('sell'):
            sell_fee = Decimal(tx.get('sell').get('fee', {}).get('amount', 0))

        native_dollar_amount = abs(Decimal(tx.get('native_amount').get('amount'))) + sell_fee
        if coin_qty_amount and native_dollar_amount != 0:
            num = native_dollar_amount / coin_qty_amount
            return str(num.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
        return 0
    return 0


def get_trade_id(tx):
    if tx.get('advanced_trade_fill'):
        return tx.get('advanced_trade_fill').get('order_id')
    elif tx.get('sell'):
        return tx.get('sell').get('id')
    else:
        return None


def get_product_id(tx):
    if tx.get('advanced_trade_fill'):
        return tx.get('advanced_trade_fill').get('product_id')
    elif tx.get('amount'):
        return f"{tx.get('amount', {}).get('currency', '')}"
    else:
        return None


def get_commission(tx):
    if tx.get('fee'):
        return tx.get('fee', {}).get('amount', 0)
    elif tx.get('sell'):
        return tx.get('sell').get('fee', {}).get('amount', 0)
    elif tx.get('advanced_trade_fill'):
        return tx.get('advanced_trade_fill').get('commission')
    else:
        return 0


def get_deposits(account_id):
    """Fetches all transactions for the given account ID using pagination and creates CoinbaseOrder objects."""
    transactions = fetch_paginated_data("GET", f"/v2/accounts/{account_id}/withdrawals")

    print("Deposits:")
    coinbase_orders = []
    for tx in transactions:
        print(
            f"- Deposit ID: {tx['id']}, Amount: {tx['amount']['amount']} {tx['amount']['currency']}, Status: {tx['status']}")

        # Map transaction data to CoinbaseOrder fields
        order_data = {
            'orderId': tx.get('id'),
            'commission': Decimal(tx.get('fee', {}).get('amount', 0)) if tx.get('fee') else Decimal(0),
            'entry_id': None,  # Not available in transaction data
            'liquidity_indicator': None,  # Not available in transaction data
            'coinbase_order_id': tx.get('id'),
            'price': Decimal(tx.get('amount', {}).get('amount', 0)) if tx.get('amount') else Decimal(0),
            'product_id': f"{tx.get('amount', {}).get('currency', '')}-USD" if tx.get('amount') else None,
            'retail_portfolio_id': None,  # Not available in transaction data
            'sequence_timestamp': tx.get('created_at'),
            'side': tx.get('type'),  # e.g., 'buy' or 'sell'
            'size': Decimal(tx.get('amount', {}).get('amount', 0)) if tx.get('amount') else Decimal(0),
            'size_in_quote': False,  # Not available in transaction data
            'trade_id': tx.get('id'),
            'trade_time': tx.get('created_at'),
            'trade_type': tx.get('type'),  # e.g., 'buy' or 'sell'
            'user_id': None  # Not available in transaction data
        }

        # Create a CoinbaseOrder object
        coinbase_order = CoinbaseOrder(order_data)
        coinbase_orders.append(coinbase_order)

    return coinbase_orders
