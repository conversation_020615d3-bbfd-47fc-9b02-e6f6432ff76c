from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional
import pandas as pd


class TimeFrame(Enum):
    M1 = "1m"
    M3 = "3m"
    M5 = "5m"
    M15 = "15m"
    M30 = "30m"
    H1 = "1h"
    H2 = "2h"
    H4 = "4h"
    H6 = "6h"
    H8 = "8h"
    H12 = "12h"
    D1 = "1d"
    D3 = "3d"
    W1 = "1w"
    M1_CAL = "1M"

    @staticmethod
    def from_str(v: str) -> "TimeFrame":
        v = v.strip()
        for tf in TimeFrame:
            if tf.value == v:
                return tf
        raise ValueError(f"Unsupported timeframe: {v}")

    def seconds(self) -> int:
        mapping = {
            TimeFrame.M1: 60,
            TimeFrame.M3: 180,
            TimeFrame.M5: 300,
            TimeFrame.M15: 900,
            TimeFrame.M30: 1800,
            TimeFrame.H1: 3600,
            TimeFrame.H2: 7200,
            TimeFrame.H4: 14400,
            TimeFrame.H6: 21600,
            TimeFrame.H8: 28800,
            TimeFrame.H12: 43200,
            TimeFrame.D1: 86400,
            TimeFrame.D3: 259200,
            TimeFrame.W1: 604800,
            TimeFrame.M1_CAL: 2592000,
        }
        return mapping[self]


class ExchangeProvider(ABC):
    """Abstract base for an exchange market-data provider."""

    @abstractmethod
    def name(self) -> str:
        ...

    @abstractmethod
    def get_supported_timeframes(self) -> List[TimeFrame]:
        ...

    @abstractmethod
    def normalize_symbol(self, symbol: str) -> str:
        ...

    @abstractmethod
    def get_symbols(self) -> List[str]:
        ...

    @abstractmethod
    def get_candles(
        self,
        symbol: str,
        timeframe: TimeFrame,
        limit: int = 500,
        start_ms: Optional[int] = None,
        end_ms: Optional[int] = None,
    ) -> pd.DataFrame:
        """Return a DataFrame indexed by open_time (UTC) with OHLCV and close_time.
        Required columns: open, high, low, close, volume, close_time
        """
        ...


@dataclass
class Candle:
    time: int  # epoch seconds (open time)
    open: float
    high: float
    low: float
    close: float
    volume: float

    @staticmethod
    def from_df_row(ts_ms: int, row: pd.Series) -> "Candle":
        return Candle(
            time=int(ts_ms // 1000),
            open=float(row["open"]),
            high=float(row["high"]),
            low=float(row["low"]),
            close=float(row["close"]),
            volume=float(row.get("volume", 0.0)),
        )

