# """
# Bybit Real-time Client
#
# Connects to the Japan server's Server-Sent Events stream to receive real-time order updates.
# This replaces the direct WebSocket connection since Bybit blocks USA IPs.
# """
#
# import logging
# import requests
# import json
# import time
# import threading
# from typing import Dict, Callable, List, Optional
# from datetime import datetime, timezone
# import queue
#
# logger = logging.getLogger(__name__)
#
#
# class BybitRealtimeClient:
#     """Client for receiving real-time updates from Japan server"""
#
#     def __init__(self, japan_server_url: str = "http://***************:8000"):
#         self.japan_server_url = japan_server_url
#         self.stream_url = f"{japan_server_url}/bybit/stream/orders"
#         self.api_headers = {"x-api-token": "yoursecret123"}
#
#         self.is_connected = False
#         self.connection_thread = None
#         self.should_reconnect = True
#
#         # Event handlers
#         self.order_handlers: List[Callable] = []
#         self.connection_handlers: List[Callable] = []
#         self.error_handlers: List[Callable] = []
#
#         # Message processing
#         self.message_queue = queue.Queue()
#         self.processing_thread = None
#
#         # Connection stats
#         self.last_message_time = None
#         self.messages_received = 0
#         self.reconnect_attempts = 0
#         self.max_reconnect_attempts = 10
#
#     def add_order_handler(self, handler: Callable[[Dict], None]):
#         """Add handler for order updates"""
#         self.order_handlers.append(handler)
#         logger.info(f"📋 Added order handler: {handler.__name__}")
#
#     def add_connection_handler(self, handler: Callable[[str], None]):
#         """Add handler for connection events"""
#         self.connection_handlers.append(handler)
#
#     def add_error_handler(self, handler: Callable[[Exception], None]):
#         """Add handler for errors"""
#         self.error_handlers.append(handler)
#
#     def _notify_connection_handlers(self, event: str):
#         """Notify connection event handlers"""
#         for handler in self.connection_handlers:
#             try:
#                 handler(event)
#             except Exception as e:
#                 logger.error(f"❌ Error in connection handler: {e}")
#
#     def _notify_error_handlers(self, error: Exception):
#         """Notify error handlers"""
#         for handler in self.error_handlers:
#             try:
#                 handler(error)
#             except Exception as e:
#                 logger.error(f"❌ Error in error handler: {e}")
#
#     def _process_messages(self):
#         """Process messages from the queue"""
#         logger.info("🔄 Starting message processing thread")
#
#         while True:
#             try:
#                 # Get message from queue (blocking)
#                 message = self.message_queue.get(timeout=1)
#
#                 if message.get("type") == "order_update":
#                     self._handle_order_update(message.get("data", {}))
#                 elif message.get("type") == "connected":
#                     logger.info("✅ Connected to Japan server stream")
#                     self._notify_connection_handlers("connected")
#
#                 self.message_queue.task_done()
#
#             except queue.Empty:
#                 continue
#             except Exception as e:
#                 logger.error(f"❌ Error processing message: {e}")
#                 self._notify_error_handlers(e)
#
#     def _handle_order_update(self, order_data: Dict):
#         """Handle order update from Japan server"""
#         try:
#             order_id = order_data.get("orderId")
#             status = order_data.get("orderStatus")
#             symbol = order_data.get("symbol")
#
#             logger.info(f"📋 Order update: {order_id} ({symbol}) -> {status}")
#
#             # Update stats
#             self.messages_received += 1
#             self.last_message_time = datetime.now(timezone.utc)
#
#             # Call all registered order handlers
#             for handler in self.order_handlers:
#                 try:
#                     handler(order_data)
#                 except Exception as e:
#                     logger.error(f"❌ Error in order handler: {e}")
#
#         except Exception as e:
#             logger.error(f"❌ Error handling order update: {e}")
#
#     def _connect_stream(self):
#         """Connect to the Server-Sent Events stream"""
#         while self.should_reconnect:
#             try:
#                 logger.info(f"🔌 Connecting to Japan server stream: {self.stream_url}")
#
#                 # Make streaming request
#                 response = requests.get(
#                     self.stream_url,
#                     headers=self.api_headers,
#                     stream=True,
#                     timeout=30
#                 )
#
#                 if response.status_code == 200:
#                     self.is_connected = True
#                     self.reconnect_attempts = 0
#                     logger.info("✅ Connected to Japan server stream")
#
#                     # Process the stream
#                     for line in response.iter_lines(decode_unicode=True):
#                         if not self.should_reconnect:
#                             break
#
#                         if line.startswith("data: "):
#                             try:
#                                 data = json.loads(line[6:])  # Remove "data: " prefix
#                                 self.message_queue.put(data)
#                             except json.JSONDecodeError as e:
#                                 logger.warning(f"⚠️ Invalid JSON in stream: {line[:100]}")
#
#                         # Keep connection alive
#                         time.sleep(0.01)
#
#                 else:
#                     logger.error(f"❌ Failed to connect to stream: HTTP {response.status_code}")
#                     self._notify_error_handlers(Exception(f"HTTP {response.status_code}"))
#
#             except requests.exceptions.RequestException as e:
#                 logger.error(f"❌ Connection error: {e}")
#                 self._notify_error_handlers(e)
#             except Exception as e:
#                 logger.error(f"❌ Unexpected error in stream connection: {e}")
#                 self._notify_error_handlers(e)
#
#             # Connection lost
#             self.is_connected = False
#             self._notify_connection_handlers("disconnected")
#
#             if self.should_reconnect:
#                 self.reconnect_attempts += 1
#                 if self.reconnect_attempts <= self.max_reconnect_attempts:
#                     wait_time = min(30, 2 ** self.reconnect_attempts)  # Exponential backoff
#                     logger.info(f"🔄 Reconnecting in {wait_time}s (attempt {self.reconnect_attempts})")
#                     time.sleep(wait_time)
#                 else:
#                     logger.error(f"❌ Max reconnection attempts ({self.max_reconnect_attempts}) reached")
#                     break
#
#         logger.info("🛑 Stream connection thread stopped")
#
#     def start(self):
#         """Start the real-time client"""
#         if self.is_connected:
#             logger.warning("⚠️ Client is already connected")
#             return
#
#         logger.info("🚀 Starting Bybit real-time client")
#
#         self.should_reconnect = True
#
#         # Start message processing thread
#         if not self.processing_thread or not self.processing_thread.is_alive():
#             self.processing_thread = threading.Thread(target=self._process_messages, daemon=True)
#             self.processing_thread.start()
#
#         # Start connection thread
#         self.connection_thread = threading.Thread(target=self._connect_stream, daemon=True)
#         self.connection_thread.start()
#
#         logger.info("✅ Real-time client started")
#
#     def stop(self):
#         """Stop the real-time client"""
#         logger.info("🛑 Stopping Bybit real-time client")
#
#         self.should_reconnect = False
#         self.is_connected = False
#
#         # Wait for threads to finish
#         if self.connection_thread and self.connection_thread.is_alive():
#             self.connection_thread.join(timeout=5)
#
#         logger.info("✅ Real-time client stopped")
#
#     def start_monitoring_orders(self, order_ids: List[str]):
#         """Start monitoring specific orders on Japan server"""
#         try:
#             url = f"{self.japan_server_url}/bybit/monitor/start"
#             response = requests.post(url, json=order_ids, headers=self.api_headers)
#
#             if response.status_code == 200:
#                 result = response.json()
#                 logger.info(f"📊 Started monitoring {len(order_ids)} orders")
#                 return result
#             else:
#                 logger.error(f"❌ Failed to start monitoring: HTTP {response.status_code}")
#                 return None
#
#         except Exception as e:
#             logger.error(f"❌ Error starting order monitoring: {e}")
#             return None
#
#     def stop_monitoring(self):
#         """Stop order monitoring on Japan server"""
#         try:
#             url = f"{self.japan_server_url}/bybit/monitor/stop"
#             response = requests.post(url, headers=self.api_headers)
#
#             if response.status_code == 200:
#                 logger.info("🛑 Stopped order monitoring")
#                 return response.json()
#             else:
#                 logger.error(f"❌ Failed to stop monitoring: HTTP {response.status_code}")
#                 return None
#
#         except Exception as e:
#             logger.error(f"❌ Error stopping order monitoring: {e}")
#             return None
#
#     def get_status(self) -> Dict:
#         """Get client status"""
#         return {
#             "connected": self.is_connected,
#             "messages_received": self.messages_received,
#             "last_message_time": self.last_message_time.isoformat() if self.last_message_time else None,
#             "reconnect_attempts": self.reconnect_attempts,
#             "handlers": {
#                 "order_handlers": len(self.order_handlers),
#                 "connection_handlers": len(self.connection_handlers),
#                 "error_handlers": len(self.error_handlers)
#             }
#         }
#
#
# # Global client instance
# _client: Optional[BybitRealtimeClient] = None
#
#
# def get_realtime_client() -> BybitRealtimeClient:
#     """Get or create global real-time client"""
#     global _client
#
#     if _client is None:
#         _client = BybitRealtimeClient()
#
#     return _client
#
#
# def start_realtime_updates():
#     """Start real-time updates"""
#     client = get_realtime_client()
#     client.start()
#     return client
#
#
# # Example handlers
# def example_order_handler(order_data: Dict):
#     """Example order update handler"""
#     order_id = order_data.get("orderId")
#     status = order_data.get("orderStatus")
#     symbol = order_data.get("symbol")
#     filled_qty = order_data.get("cumExecQty", "0")
#
#     logger.info(f"📋 Order Update: {order_id} ({symbol}) -> {status}, Filled: {filled_qty}")
#
#     # Here you could:
#     # - Update your local database
#     # - Send notifications to frontend
#     # - Update trade status
#     # - Trigger other actions based on order status
#
#
# def example_connection_handler(event: str):
#     """Example connection event handler"""
#     logger.info(f"🔌 Connection event: {event}")
#
#
# if __name__ == "__main__":
#     # Example usage
#     logging.basicConfig(level=logging.INFO)
#
#     # Create and start client
#     client = start_realtime_updates()
#
#     # Add handlers
#     client.add_order_handler(example_order_handler)
#     client.add_connection_handler(example_connection_handler)
#
#     # Start monitoring some orders (example)
#     # client.start_monitoring_orders(["order_123", "order_456"])
#
#     try:
#         while True:
#             time.sleep(1)
#             # Print status every 30 seconds
#             if int(time.time()) % 30 == 0:
#                 status = client.get_status()
#                 logger.info(f"📊 Client status: {status}")
#     except KeyboardInterrupt:
#         logger.info("🛑 Shutting down...")
#         client.stop()
