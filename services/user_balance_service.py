"""
User Balance Service

Handles balance fetching for user-specific exchange connections using their stored API credentials.
"""

import logging
import requests
from decimal import Dec<PERSON><PERSON>
from typing import Dict, Any, Optional

from models.exchange_connection import ExchangeConnection

logger = logging.getLogger(__name__)


class UserBalanceService:
    """Service for fetching user balances from various exchanges"""
    
    @staticmethod
    def get_balance(connection: ExchangeConnection) -> Dict[str, Any]:
        """
        Get balance for a specific exchange connection.
        
        Args:
            connection: ExchangeConnection instance with API credentials
            
        Returns:
            dict: {"success": bool, "balance": float, "currency": str, "error": str}
        """
        try:
            exchange_name = connection.exchange_name.lower()
            
            if exchange_name == "bybit":
                return UserBalanceService._get_bybit_balance(connection)
            elif exchange_name == "coinbase":
                return UserBalanceService._get_coinbase_balance(connection)
            elif exchange_name in ["binance", "blofin", "hyperliquid", "woox", "phemex", "deribit", "bitget"]:
                return {
                    "success": False,
                    "balance": 0.0,
                    "currency": "USDT",
                    "error": f"{exchange_name.title()} integration not yet implemented"
                }
            else:
                return {
                    "success": False,
                    "balance": 0.0,
                    "currency": "USDT",
                    "error": f"Unknown exchange: {exchange_name}"
                }
                
        except Exception as e:
            logger.error(f"❌ Error fetching balance for {connection.exchange_name}: {e}")
            return {
                "success": False,
                "balance": 0.0,
                "currency": "USDT",
                "error": f"Error fetching balance: {str(e)}"
            }
    
    @staticmethod
    def _get_bybit_balance(connection: ExchangeConnection) -> Dict[str, Any]:
        """
        Fetch Bybit balance using services.bybit with session-based monitoring.
        """
        try:
            # Delegate to Bybit helper which starts a session and requests balance using user creds
            from services.bybit import Bybit
            bal_str = Bybit.getAccountBalance(connection)
            balance = float(bal_str)

            logger.info(f"✅ Bybit balance fetched: ${balance:.2f}")
            return {"success": True, "balance": balance, "currency": "USDT", "error": None}

        except requests.exceptions.Timeout:
            return {"success": False, "balance": 0.0, "currency": "USDT", "error": "Request timeout - Bybit server may be slow"}
        except requests.exceptions.ConnectionError:
            return {"success": False, "balance": 0.0, "currency": "USDT", "error": "Connection error - Check network connection"}
        except requests.exceptions.HTTPError as e:
            status = getattr(e.response, 'status_code', 'unknown')
            return {"success": False, "balance": 0.0, "currency": "USDT", "error": f"Bybit API error: HTTP {status}"}
        except Exception as e:
            logger.error(f"❌ Bybit balance error: {e}")
            return {"success": False, "balance": 0.0, "currency": "USDT", "error": f"Bybit API error: {str(e)}"}

    @staticmethod
    def _get_coinbase_balance(connection: ExchangeConnection) -> Dict[str, Any]:
        """
        Fetch Coinbase balance using user's stored credentials.
        
        Note: Currently uses the existing _load_api_keys method.
        TODO: Update to use database credentials.
        """
        try:
            # For now, use the existing method that loads from JournalKeys.json
            # TODO: Create a new method that uses the decrypted database credentials
            from services.coinbase_advanced import get_futures_balance_summary
            
            summary = get_futures_balance_summary()
            if summary and "balance_summary" in summary:
                balance = float(summary["balance_summary"]["total_usd_balance"]["value"])
                
                logger.info(f"✅ Coinbase balance fetched: ${balance:.2f}")
                
                return {
                    "success": True,
                    "balance": balance,
                    "currency": "USD",
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "balance": 0.0,
                    "currency": "USD",
                    "error": "No balance data received from Coinbase"
                }
                
        except Exception as e:
            logger.error(f"❌ Coinbase balance error: {e}")
            return {
                "success": False,
                "balance": 0.0,
                "currency": "USD",
                "error": f"Coinbase API error: {str(e)}"
            }
    
    @staticmethod
    def test_connection(connection: ExchangeConnection) -> Dict[str, Any]:
        """
        Test if an exchange connection is working.
        
        Args:
            connection: ExchangeConnection instance to test
            
        Returns:
            dict: {"success": bool, "message": str, "error": str}
        """
        try:
            balance_result = UserBalanceService.get_balance(connection)
            
            if balance_result["success"]:
                return {
                    "success": True,
                    "message": f"Connection successful - Balance: {balance_result['balance']:.2f} {balance_result['currency']}",
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "message": "Connection failed",
                    "error": balance_result["error"]
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "Connection test failed",
                "error": str(e)
            }
    
    @staticmethod
    def get_supported_exchanges() -> list:
        """Get list of supported exchanges with their integration status"""
        return [
            {"name": "bybit", "supported": True, "status": "Full integration"},
            {"name": "coinbase", "supported": True, "status": "Full integration"},
            {"name": "binance", "supported": False, "status": "Not implemented"},
            {"name": "blofin", "supported": False, "status": "Not implemented"},
            {"name": "hyperliquid", "supported": False, "status": "Not implemented"},
            {"name": "woox", "supported": False, "status": "Not implemented"},
            {"name": "phemex", "supported": False, "status": "Not implemented"},
            {"name": "deribit", "supported": False, "status": "Not implemented"},
            {"name": "bitget", "supported": False, "status": "Not implemented"}
        ]


# Convenience functions for backward compatibility
def get_user_balance(connection: ExchangeConnection) -> Dict[str, Any]:
    """Convenience function to get user balance"""
    return UserBalanceService.get_balance(connection)


def test_user_connection(connection: ExchangeConnection) -> Dict[str, Any]:
    """Convenience function to test user connection"""
    return UserBalanceService.test_connection(connection)
