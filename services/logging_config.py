"""
Logging configuration for API services.

This module provides easy-to-use functions to configure logging for API requests.
"""

import logging
import os
import sys
from datetime import datetime


def configure_api_logging(log_level=logging.INFO, log_to_file=True, log_to_console=True):
    """
    Configure logging for all API services.
    
    Args:
        log_level: Logging level (default: logging.INFO)
        log_to_file: Whether to log to file (default: True)
        log_to_console: Whether to log to console (default: True)
    
    Returns:
        logging.Logger: Configured logger instance
    """

    # Create logs directory if it doesn't exist
    if log_to_file:
        os.makedirs('logs', exist_ok=True)

    # Configure the services.bybit logger
    logger = logging.getLogger('services.bybit')
    logger.setLevel(log_level)

    # Clear any existing handlers to avoid duplicates
    logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Console handler
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # File handler
    if log_to_file:
        # Create filename with current date
        log_filename = f"logs/bybit_api_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_filename)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # Prevent propagation to root logger to avoid duplicate messages
    logger.propagate = False

    return logger


def configure_all_api_logging():
    """
    Configure logging for all API services in the application.
    This can be extended to include other services like Coinbase, etc.
    """

    # Configure Bybit API logging
    bybit_logger = configure_api_logging()

    # Future: Add other API service loggers here
    # coinbase_logger = logging.getLogger('services.coinbase_advanced')
    # coinbase_logger.setLevel(logging.INFO)
    # ... configure similarly

    return {
        'bybit': bybit_logger
    }


def enable_debug_logging():
    """
    Enable debug-level logging for detailed API request/response information.
    """
    logger = logging.getLogger('services.bybit')
    logger.setLevel(logging.DEBUG)

    # Update all handlers to debug level
    for handler in logger.handlers:
        handler.setLevel(logging.DEBUG)

    print("Debug logging enabled for API requests")


def disable_api_logging():
    """
    Disable API request logging.
    """
    logger = logging.getLogger('services.bybit')
    logger.setLevel(logging.CRITICAL)  # Only show critical errors
    print("API request logging disabled")
