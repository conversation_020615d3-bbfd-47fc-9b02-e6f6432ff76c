from __future__ import annotations

from typing import List, Optional

import pandas as pd
from binance.client import Client

from services.exchange_interface import ExchangeProvider, TimeFrame
from services import binance


class BinanceProvider(ExchangeProvider):
    """Binance US provider using existing binance.py functions."""

    _tf_map = {
        TimeFrame.M1: Client.KLINE_INTERVAL_1MINUTE,
        TimeFrame.M3: Client.KLINE_INTERVAL_3MINUTE,
        TimeFrame.M5: Client.KLINE_INTERVAL_5MINUTE,
        TimeFrame.M15: Client.KLINE_INTERVAL_15MINUTE,
        TimeFrame.M30: Client.KLINE_INTERVAL_30MINUTE,
        TimeFrame.H1: Client.KLINE_INTERVAL_1HOUR,
        TimeFrame.H2: Client.KLINE_INTERVAL_2HOUR,
        TimeFrame.H4: Client.KLINE_INTERVAL_4HOUR,
        TimeFrame.H6: Client.KLINE_INTERVAL_6HOUR,
        TimeFrame.H8: Client.KLINE_INTERVAL_8HOUR,
        TimeFrame.H12: Client.KLINE_INTERVAL_12HOUR,
        TimeFrame.D1: Client.KLINE_INTERVAL_1DAY,
        TimeFrame.D3: Client.KLINE_INTERVAL_3DAY,
        TimeFrame.W1: Client.KLINE_INTERVAL_1WEEK,
        TimeFrame.M1_CAL: Client.KLINE_INTERVAL_1MONTH,
    }

    def name(self) -> str:
        return "binance"

    def get_supported_timeframes(self) -> List[TimeFrame]:
        return list(self._tf_map.keys())

    def normalize_symbol(self, symbol: str) -> str:
        return symbol.upper()

    def get_symbols(self) -> List[str]:
        # Return common USDT pairs available on Binance US
        return [
            "BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT", "LINKUSDT",
            "LTCUSDT", "BCHUSDT", "XLMUSDT", "ETCUSDT", "XRPUSDT", "ATOMUSDT",
            "MATICUSDT", "ALGOUSDT", "VETUSDT", "SHIBUSDT", "DOGEUSDT"
        ]

    def get_candles(
            self,
            symbol: str,
            timeframe: TimeFrame,
            limit: int = 500,
            start_ms: Optional[int] = None,
            end_ms: Optional[int] = None,
    ) -> pd.DataFrame:
        # Use the existing binance.py function which handles Binance US API
        interval = self._tf_map[timeframe]
        return binance.get_candlestick_dataframe(
            symbol=self.normalize_symbol(symbol),
            interval=interval,
            limit=min(max(1, limit), 1500),
            start_ms=start_ms,
            end_ms=end_ms
        )
