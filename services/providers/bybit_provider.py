from __future__ import annotations

from typing import List, Optional

import pandas as pd

from services.bybit import Bybit
from services.exchange_interface import ExchangeProvider, TimeFrame


class BybitProvider(ExchangeProvider):
    def name(self) -> str:
        return "bybit"

    def get_supported_timeframes(self) -> List[TimeFrame]:
        return [
            TimeFrame.M1, TimeFrame.M3, TimeFrame.M5, TimeFrame.M15, TimeFrame.M30,
            TimeFrame.H1, TimeFrame.H2, TimeFrame.H4, TimeFrame.H6, TimeFrame.H12,
            TimeFrame.D1, TimeFrame.W1, TimeFrame.M1_CAL,
        ]

    def normalize_symbol(self, symbol: str) -> str:
        return symbol.upper()

    def get_symbols(self) -> List[str]:
        # Call bybit service and return symbols
        return Bybit.get_symbols()

    def _interval_to_ms(self, tf: TimeFrame) -> int:
        sec = tf.seconds()
        return sec * 1000

    def get_candles(
            self,
            symbol: str,
            timeframe: TimeFrame,
            limit: int = 500,
            start_ms: Optional[int] = None,
            end_ms: Optional[int] = None,
    ) -> pd.DataFrame:
        # Convert TimeFrame enum to string format expected by Bybit API
        timeframe_str = timeframe.value  # e.g., TimeFrame.H1.value = "1h"
        df = Bybit.get_candlestick_dataframe(symbol, timeframe_str, limit, start_ms, end_ms)
        return df
