#!/usr/bin/env python3
"""
Test script for order execution service integration with session-based monitoring.

This script tests the updated order execution service to ensure it properly:
1. Accepts ExchangeConnection parameters
2. Integrates with the session-based WebSocket monitoring system
3. Uses per-request credentials for API calls
4. Handles errors gracefully
"""

import logging
import sys
import os
from decimal import Decimal
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported"""
    logger.info("🔍 Testing imports...")
    
    try:
        from services.order_execution_service import OrderExecutionService, OrderExecutionResult, TradeExecutionResult
        from models.exchange_connection import ExchangeConnection
        from models.trade import Trade, TradeStatus, TradeDirection
        from models.order import Order, BuySell
        from services.trade_creation_service import OrderType, OrderStatus
        
        logger.info("✅ All imports successful")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False

def test_method_signatures():
    """Test that method signatures are updated correctly"""
    logger.info("🔍 Testing method signatures...")
    
    try:
        from services.order_execution_service import OrderExecutionService
        from services.bybit import Bybit
        import inspect
        
        # Test execute_setup_trade signature
        sig = inspect.signature(OrderExecutionService.execute_setup_trade)
        params = list(sig.parameters.keys())
        
        assert 'trade' in params, "execute_setup_trade should have 'trade' parameter"
        assert 'connection' in params, "execute_setup_trade should have 'connection' parameter"
        
        # Test Bybit method signatures
        submit_order_sig = inspect.signature(Bybit.submit_order)
        submit_order_params = list(submit_order_sig.parameters.keys())
        assert 'connection' in submit_order_params, "submit_order should have 'connection' parameter"
        
        submit_stop_loss_sig = inspect.signature(Bybit.submit_stop_loss)
        submit_stop_loss_params = list(submit_stop_loss_sig.parameters.keys())
        assert 'connection' in submit_stop_loss_params, "submit_stop_loss should have 'connection' parameter"
        
        submit_take_profit_sig = inspect.signature(Bybit.submit_take_profit)
        submit_take_profit_params = list(submit_take_profit_sig.parameters.keys())
        assert 'connection' in submit_take_profit_params, "submit_take_profit should have 'connection' parameter"
        
        logger.info("✅ Method signatures updated correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Method signature test failed: {e}")
        return False

def test_session_monitoring_methods():
    """Test that session monitoring methods exist"""
    logger.info("🔍 Testing session monitoring methods...")
    
    try:
        from services.order_execution_service import OrderExecutionService
        
        # Check that new session methods exist
        assert hasattr(OrderExecutionService, '_start_monitoring_session'), "Missing _start_monitoring_session method"
        assert hasattr(OrderExecutionService, '_add_orders_to_session'), "Missing _add_orders_to_session method"
        assert hasattr(OrderExecutionService, '_stop_monitoring_session'), "Missing _stop_monitoring_session method"
        
        logger.info("✅ Session monitoring methods exist")
        return True
        
    except Exception as e:
        logger.error(f"❌ Session monitoring test failed: {e}")
        return False

def test_environment_variables():
    """Test environment variable configuration"""
    logger.info("🔍 Testing environment variables...")
    
    # Check if required environment variables are set
    proxy_url = os.getenv("JAPAN_PROXY_URL")
    proxy_token = os.getenv("JAPAN_PROXY_API_TOKEN")
    enable_sessions = os.getenv("BYBIT_ENABLE_SESSIONS")
    
    logger.info(f"JAPAN_PROXY_URL: {'✅ Set' if proxy_url else '❌ Not set'}")
    logger.info(f"JAPAN_PROXY_API_TOKEN: {'✅ Set' if proxy_token else '❌ Not set'}")
    logger.info(f"BYBIT_ENABLE_SESSIONS: {enable_sessions or 'Not set (default: false)'}")
    
    if not proxy_url:
        logger.warning("⚠️ JAPAN_PROXY_URL not set - will use default: http://128.199.252.188:8000")
    
    if not proxy_token:
        logger.warning("⚠️ JAPAN_PROXY_API_TOKEN not set - session monitoring will be disabled")
    
    return True

def test_mock_execution():
    """Test execution flow with mock data (without actual API calls)"""
    logger.info("🔍 Testing mock execution flow...")
    
    try:
        from models.exchange_connection import ExchangeConnection
        from services.order_execution_service import OrderExecutionService
        
        # Create mock connection
        mock_connection = ExchangeConnection(
            id=1,
            user_id=1,
            exchange_name="bybit",
            api_key="test_key",
            api_secret_encrypted="encrypted_secret",
            is_testnet=True,
            is_active=True
        )
        
        # Test that connection parameter is accepted
        # Note: This won't actually execute since we don't have a real trade object
        logger.info("✅ Mock connection created successfully")
        logger.info(f"   Exchange: {mock_connection.exchange_name}")
        logger.info(f"   Testnet: {mock_connection.is_testnet}")
        logger.info(f"   API Key: {mock_connection.api_key[:8]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Mock execution test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    logger.info("🚀 Starting Order Execution Service Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Method Signatures", test_method_signatures),
        ("Session Monitoring", test_session_monitoring_methods),
        ("Environment Variables", test_environment_variables),
        ("Mock Execution", test_mock_execution),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All integration tests passed! The order execution service is ready.")
    else:
        logger.warning("⚠️ Some tests failed. Please review the issues above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
