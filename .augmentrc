{
  "ignore": [
    "node_modules",
    "dist",
    "build",
    "venv",
    "__pycache__",
    ".git"
  ],
  "agent": {
    "memories": [
      "Always provide only the updated/changed code in responses, keeping code snippets as brief as possible",
      "Always use Decimal for financial calculations",
      "Follow PEP 8 style guidelines for Python code",
      "Maintain existing code style and patterns when making changes",
      "Prefer simple, readable solutions over complex ones - avoid over-engineering when a straightforward approach will work"
      "Do not duplicate code, move existing code to a function in helper.py if necessary",
    ]
  }
}