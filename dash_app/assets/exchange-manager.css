/* Exchange Account Manager Styles */
.add-exchange-modal .modal-dialog {
    max-width: 800px;
}

.exchange-selection-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
    border-color: #0d6efd !important;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
}

.step-number.active {
    background-color: #0d6efd;
    color: white;
}

.step-text {
    font-size: 0.9rem;
    color: #6c757d;
}

.exchange-accounts-container .card {
    transition: all 0.2s ease;
}

.exchange-accounts-container .card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #28a745;
}

/* Button hover effects */
.btn-outline-primary:hover,
.btn-outline-danger:hover {
    transform: scale(1.05);
}

/* Modal animations */
.add-exchange-modal .modal-content {
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Progress indicator line */
.step-item::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    width: 100%;
    height: 2px;
    background-color: #e9ecef;
    z-index: -1;
}

.step-item:last-child::after {
    display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .add-exchange-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem;
    }
    
    .step-text {
        font-size: 0.8rem;
    }
    
    .step-number {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }
}
