(function () {
  function calculateSidebarHeight() {
    // Sidebar structure calculation
    const sidebarPadding = 24; // 12px top + 12px bottom
    const buttonHeight = 60;
    const buttonCount = 8; // 1 + 4 + 3 buttons
    const groupGaps = 24; // 2 gaps × 12px
    const internalGaps = 40; // 5 internal gaps × 8px
    const groupPadding = 24; // 3 groups × 8px
    const separators = 2; // 2 separators × 1px

    return sidebarPadding + (buttonCount * buttonHeight) + groupGaps + internalGaps + groupPadding + separators;
  }

  function setIframeHeight() {
    const iframe = document.getElementById('chart-iframe');
    console.log('setIframeHeight');
    if (!iframe) return;

    const calculatedHeight = calculateSidebarHeight();
    console.log(`📏 Setting iframe height to calculated sidebar height: ${calculatedHeight}px`);
    iframe.style.height = calculatedHeight + 'px';
  }

  // Listen for ready message from iframe
  window.addEventListener('message', function(e) {
    if (e && e.data && e.data.type === 'chart_ready') {
      console.log('📏 Chart iframe is ready, setting height');
      setIframeHeight();
    }
  });

  console.log('the listener is loaded');

  // Handle window resize (sidebar height doesn't change, but good practice)
  window.addEventListener('resize', setIframeHeight);
})();