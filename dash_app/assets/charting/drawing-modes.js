// Drawing modes - extracted from GOOD CHART.html
// This file contains mode management and UI state handling

// ----- Modes -----
function setMode(newMode){
  mode = newMode;
  btnNormal.classList.toggle('active', mode==='normal');
  btnLine.classList.toggle('active', mode==='line');
  btnTrendline.classList.toggle('active', mode==='trendline');
  btnFib.classList.toggle('active',  mode==='fib');
  btnRect.classList.toggle('active', mode==='rect');

  // Enable/disable pan and zoom based on mode
  setPanZoomEnabled(mode === 'normal');
}
btnNormal.onclick = () => setMode('normal');
btnLine.onclick = () => setMode('line');
btnTrendline.onclick = () => setMode('trendline');
btnFib.onclick  = () => setMode('fib');
btnRect.onclick = () => setMode('rect');
setMode('normal');

// ----- Click → single line (only in Line mode) -----
chart.subscribeClick((param) => {
  if (mode !== 'line') return;
  if (!param?.point || typeof param.point.y !== 'number') return;

  const price = snap(series.coordinateToPrice(param.point.y));
  const clickTime = chart.timeScale().coordinateToTime(param.point.x);

  if (clickTime == null) {
    console.error('Invalid time coordinate for line placement');
    return;
  }

  // Create time-bounded line starting from click point
  const currentData = series.data();
  const lastTime = currentData && currentData.length > 0 ?
    currentData[currentData.length - 1].time : clickTime;
  const lineEndTime = Math.max(clickTime, lastTime + (3600 * 24 * 7)); // Extend 7 days into future

  createFibLevel(price, clickTime, lineEndTime, {
    color:'#58a6ff',
    kind:'single',
    title:`Line ${price.toFixed(2)}`
  });

  console.log('Time-bounded line placed at', price, 'from time', clickTime);

  // Automatically switch back to normal mode after placing a line
  setMode('normal');
});

// ----- Trendline, Fib & Rectangle: mousedown starts drawing -----
chartEl.addEventListener('mousedown', (e) => {
  if (mode === 'trendline') {
    if (!lastPoint || typeof lastPoint.y !== 'number') return;

    // Disable pan/zoom while dragging Trendline
    setPanZoomEnabled(false);

    const time = chart.timeScale().coordinateToTime(lastPoint.x);
    const price = snap(series.coordinateToPrice(lastPoint.y));

    // Validate coordinates
    if (time == null || isNaN(price)) {
      console.error('Invalid start coordinates for trendline:', { time, price });
      setPanZoomEnabled(true);
      return;
    }

    trendlineActive = true;
    trendlineStartPoint = { x: lastPoint.x, y: lastPoint.y, time, price };

    console.log('Trendline start point:', trendlineStartPoint);

    // prevent the chart from seeing this as a pan start
    e.preventDefault();
    e.stopPropagation();
  } else if (mode === 'fib') {
    if (!lastPoint || typeof lastPoint.y !== 'number') return;

    // Disable pan/zoom while dragging Fib
    setPanZoomEnabled(false);

    fibActive = true;
    fibStartPrice = snap(series.coordinateToPrice(lastPoint.y));
    fibStartTime = chart.timeScale().coordinateToTime(lastPoint.x); // capture start time

    // temp start line
    tempStart = series.createPriceLine({
      price: fibStartPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P1'
    });

    // prevent the chart from seeing this as a pan start
    e.preventDefault();
    e.stopPropagation();
  } else if (mode === 'rect') {
    if (!lastPoint || typeof lastPoint.y !== 'number') return;

    // Disable pan/zoom while dragging Rectangle
    setPanZoomEnabled(false);

    rectActive = true;
    const time = chart.timeScale().coordinateToTime(lastPoint.x);
    const price = snap(series.coordinateToPrice(lastPoint.y));
    rectStartPoint = { x: lastPoint.x, y: lastPoint.y, time, price };

    // Create initial temp lines + left vertical line
    tempRectTop = series.createPriceLine({
      price, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'Rect Start'
    });
    tempRectBottom = tempRectTop; // Start with same line, will split on mousemove

    // Create left vertical line (shows rectangle start)
    tempRectLeft = chart.addLineSeries({
      color: '#cccccc',
      lineWidth: 1,
      lineStyle: 2, // dashed
      priceLineVisible: false,
      lastValueVisible: false
    });

    // Set initial left line (just at start point)
    const currentData = series.data();
    if (currentData && currentData.length > 0) {
      const minPrice = Math.min(...currentData.map(d => d.low));
      const maxPrice = Math.max(...currentData.map(d => d.high));
      tempRectLeft.setData([
        { time, value: minPrice },
        { time, value: maxPrice }
      ]);
    }

    // prevent the chart from seeing this as a pan start
    e.preventDefault();
    e.stopPropagation();
  }
});
