<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Lightweight Charts — Lines + Fib 50%</title>
<style>
  :root { --bg:#0e1116; --panel:#161b22; --text:#c9d1d9; --muted:#8b949e; --accent:#58a6ff; }
  html, body { height:100%; }
  body { margin:0; display:grid; grid-template-rows: auto auto 1fr; height:100%;
         background:var(--bg); color:var(--text); font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
  #chart { width:100%; height:100%; }
  .top-controls { background:var(--panel); border-bottom:1px solid #222; padding:10px 12px; }
  .controls-row { display:flex; gap:8px; flex-wrap:wrap; align-items:center; justify-content:flex-start; }
  .controls-row .btn { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; }
  .controls-row .btn:hover { border-color:#3a4556; }
  .controls-row .btn.active { outline:2px solid #58a6ff60; }
  .hint-text { color:var(--muted); font-size:12px; margin-left:auto; }
  .drawings-panel { position:absolute; top:100px; right:10px; background:var(--panel); border:1px solid #222; border-radius:8px; padding:10px; min-width:280px; max-height:400px; overflow-y:auto; z-index:1000; display:none; }
  .drawings-panel.show { display:block; }
  .info-section { background:var(--panel); border-bottom:1px solid #222; padding:10px 12px; display:grid; grid-template-columns:1fr 1fr; gap:20px; }
  .mode-info, .trade-details { }
  .mode-info h4, .trade-details h4 { margin:0 0 8px 0; font-size:14px; color:var(--text); }
  .trade-entry { background:#0b1d33; border:1px solid #14304f; border-radius:6px; padding:8px; margin:4px 0; font-size:12px; }
  .trade-entry.stop-loss { border-color:#dc3545; background:#2d0a0f; }
  .trade-entry.entry { border-color:#28a745; background:#0a2d0f; }
  .trade-entry.take-profit { border-color:#ffc107; background:#2d2a0a; }
  .btn { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; }
  .btn:hover { border-color:#3a4556; }
  .btn.active { outline:2px solid #58a6ff60; }
  .lines-toggle { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; margin-left:10px; }
  .lines-toggle:hover { border-color:#3a4556; }
  .muted { color:var(--muted); font-size:12px; }
  ul { list-style:none; padding:0; margin:8px 12px 12px; }
  li { display:flex; justify-content:space-between; align-items:center; gap:8px; padding:6px 8px; border:1px solid #2b3340; border-radius:8px; margin-top:8px; }
  .pill { background:#0b1d33; border:1px solid #14304f; border-radius:999px; padding:2px 8px; font-variant-numeric: tabular-nums; }
  .groupTag { font-size:11px; opacity:0.7; border:1px solid #2b3340; border-radius:6px; padding:0 6px; margin-left:6px; }
  .stack { display:flex; gap:8px; flex-wrap:wrap; }
</style>
</head>
<body>
  <div class="top-controls">
    <div class="controls-row">
      <button id="mode-normal" class="btn active" title="Normal chart viewing mode">📊 Normal</button>
      <button id="mode-line" class="btn" title="Click to place single line">➕ Line</button>
      <button id="mode-trendline" class="btn" title="Click and drag to draw a diagonal trendline">📈 Trendline</button>
      <button id="mode-fib" class="btn" title="MouseDown set P1, drag, MouseUp set P2, creates retracement and take profit levels">∿ Fib</button>
      <button id="mode-rect" class="btn" title="Click and drag to draw a rectangle">▭ Rectangle</button>
      <button id="auto-fib" class="btn" title="Automatically draw Fibonacci retracement from most recent swing">🤖 Auto-Fib</button>
      <button id="clear" class="btn" title="Remove all lines">🧹 Clear</button>
      <button id="drawings-toggle" class="lines-toggle" title="Show/Hide Drawings Panel">📋 Drawings</button>
    </div>
  </div>

  <div class="info-section">
    <div class="mode-info">
      <h4>Current Mode</h4>
      <div class="muted" id="hint">Mode: Normal — chart viewing mode with pan and zoom enabled.</div>
    </div>
    <div class="trade-details">
      <h4>Trade Details</h4>
      <div id="trade-info" class="muted">Draw a Fibonacci retracement to generate trade plan</div>
    </div>
  </div>

  <div id="chart"></div>

  <div id="drawings-panel" class="drawings-panel">
    <strong>Drawings</strong>
    <ul id="list"></ul>
  </div>

  <!-- TradingView Lightweight Charts (MIT) -->
  <script src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"></script>
  <script>
  (function(){
    // ----- Singleton guard to avoid duplicates on reload -----
    if (window.__lw_chart__) {
      try { window.__lw_chart__.remove(); } catch(e){}
      const chartEl0 = document.getElementById('chart');
      while (chartEl0.firstChild) chartEl0.removeChild(chartEl0.firstChild);
    }

    // ----- Elements -----
    const chartEl = document.getElementById('chart');
    const listEl  = document.getElementById('list');
    const btnNormal = document.getElementById('mode-normal');
    const btnLine = document.getElementById('mode-line');
    const btnTrendline = document.getElementById('mode-trendline');
    const btnFib  = document.getElementById('mode-fib');
    const btnRect = document.getElementById('mode-rect');
    const btnAutoFib = document.getElementById('auto-fib');
    const btnClear= document.getElementById('clear');
    const btnDrawingsToggle = document.getElementById('drawings-toggle');
    const drawingsPanel = document.getElementById('drawings-panel');
    const hintEl  = document.getElementById('hint');
    const tradeInfoEl = document.getElementById('trade-info');

    // ----- Create chart -----
    const chart = window.__lw_chart__ = LightweightCharts.createChart(chartEl, {
      layout: { background: { color: '#0e1116' }, textColor: '#c9d1d9' },
      rightPriceScale: {
        autoScale: false,  // Disable auto-scaling to allow manual panning
      },
      timeScale: { borderVisible: false, timeVisible: true, secondsVisible: false },
      grid: { horzLines: { color: '#1f2633' }, vertLines: { color: '#1f2633' } },
      crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
    });

    // Toggle chart interactions (pan/zoom) while drawing
    function setPanZoomEnabled(enabled) {
      chart.applyOptions({
        handleScroll: enabled,  // disable pressed-mouse pan
        handleScale: enabled,   // disable wheel/pinch zoom
      });

      // Optional: change cursor to signal drawing mode
      chartEl.style.cursor = enabled ? "default" : "crosshair";
    }

    const series = chart.addCandlestickSeries({
      upColor: '#26a69a', downColor: '#ef5350',
      wickUpColor: '#26a69a', wickDownColor: '#ef5350',
      borderVisible: false,
      priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
    });

    // ----- Demo data -----
    const data = [];
    const t0 = Math.floor(Date.now()/1000) - 3600*24*30; // 30d hourly
    let p = 100;
    for (let i=0;i<500;i++){
      const t = t0 + i*3600;
      const o = p, h = o + Math.random()*3+1, l = o - (Math.random()*3+1), c = l + Math.random()*(h-l);
      p = c;
      data.push({ time: t, open:o, high:h, low:l, close:c });
    }
    // Add whitespace points to allow drawing beyond last candle
    const lastTs = data[data.length - 1].time;
    const candleInterval = data.length > 1 ? data[1].time - data[0].time : 3600;

    // Add several whitespace points to create drawing space
    let numWhiteSpaces = 200;
    const whitespacePoints = [];
    for (let i = 1; i <= numWhiteSpaces; i++) {
      whitespacePoints.push({ time: lastTs + (candleInterval * i) });
    }

    const extendedData = data.concat(whitespacePoints);
    series.setData(extendedData);

    // Set initial visible range to show some future space
    const futureTime = lastTs + (candleInterval * 7);
    chart.timeScale().setVisibleRange({
      from: data[Math.max(0, data.length - numWhiteSpaces)].time,
      to: futureTime
    });

    // Create baseline trendline to establish drawable area in future space
    // This prevents pan gesture from taking over when drawing beyond last candle
    const baselineTrendlineSeries = chart.addLineSeries({
      color: 'transparent', // Blue color so we can see it working
      lineWidth: 1,
      priceLineVisible: false,
      lastValueVisible: false,
      title: ''
    });

    // Create a horizontal baseline trendline at current price level
    const lastPrice = data[data.length - 1].close;
    const baselinePrice = lastPrice; // Horizontal line at current price

    // Extend baseline to the end of whitespace area to ensure full coverage
    const maxWhitespaceTime = lastTs + (candleInterval * numWhiteSpaces); // End of our whitespace points

    baselineTrendlineSeries.setData([
      { time: lastTs, value: baselinePrice },
      { time: maxWhitespaceTime, value: baselinePrice } // Extend to end of whitespace
    ]);

    console.log('Created horizontal baseline trendline for future drawing area:', {
      from: lastTs,
      to: maxWhitespaceTime,
      price: baselinePrice,
      lastPrice: lastPrice,
      whitespaceEnd: new Date(maxWhitespaceTime * 1000).toISOString()
    });

    // ----- State & helpers -----
    const pf = series.options().priceFormat || {};
    const minMove = pf.minMove ?? 0.01;
    const lines = []; // { id, price, handle, kind: 'single'|'fib1'|'fib2'|'fib50'|'fib618'|'fib786'|'fibtp236'|'fibtp618'|'rectangle'|'trendline', group?:string }
    let lastPoint = null; // last crosshair point {x,y}
    let mode = 'normal';    // 'normal' | 'line' | 'trendline' | 'fib' | 'rect'
    const fmt = (x) => Number(x).toLocaleString(undefined, { maximumFractionDigits: 8 });

    // For Rectangle mode
    let rectActive = false;
    let rectStartPoint = null; // {x, y, time, price}
    let tempRectTop = null; // temp top line
    let tempRectBottom = null; // temp bottom line
    let tempRectLeft = null; // temp left vertical line series
    let lastRectUpdate = 0; // throttle rectangle updates
    let rectUpdateTimeout = null; // for final update when mouse stops

    // For Trendline mode
    let trendlineActive = false;
    let trendlineStartPoint = null; // {x, y, time, price}
    let tempTrendlineSeries = null;
    let lastTrendlineUpdate = 0;

    // For Fib mode
    let fibActive = false;
    let fibStartPrice = null;
    let fibStartTime = null; // capture start time for time-bounded fibs
    let tempStart = null; // temp line handles
    let tempEnd = null;

    function snap(price) {
      return Math.round(price / minMove) * minMove;
    }

    function createLine(price, opts = {}) {
      const handle = series.createPriceLine({
        price,
        color: opts.color || '#58a6ff',
        lineWidth: opts.lineWidth ?? 2,
        lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
        axisLabelVisible: true,
        title: opts.title ?? String(price),
      });
      const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const rec = { id, price, handle, kind: opts.kind || 'single', group: opts.group };
      lines.push(rec);

      // Only add list item for single lines, fib sets, rectangles, and trendlines (not individual fib/rect levels)
      if (opts.kind === 'single' || opts.kind === 'fibset' || opts.kind === 'rectangle' || opts.kind === 'trendline') {
        addListItem(rec);
      }

      post('lw_line_added', { price, kind: rec.kind, group: rec.group });
      return rec;
    }

    // Create time-bounded Fibonacci level (horizontal line between two time points)
    function createFibLevel(price, startTime, endTime, opts = {}) {
      const fibSeries = chart.addLineSeries({
        color: opts.color || '#58a6ff',
        lineWidth: opts.lineWidth ?? 2,
        lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
        priceLineVisible: false,
        lastValueVisible: false,
        title: opts.title || `Fib ${price.toFixed(2)}`
      });

      // Create horizontal line data from start time to end time (or future)
      const currentData = series.data();
      const lastTime = currentData && currentData.length > 0 ?
        currentData[currentData.length - 1].time : endTime;
      const fibEndTime = Math.max(endTime, lastTime + (3600 * 24 * 7)); // Extend 7 days into future

      fibSeries.setData([
        { time: startTime, value: price },
        { time: fibEndTime, value: price }
      ]);

      const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const rec = {
        id,
        price,
        handle: fibSeries,
        kind: opts.kind || 'fibLevel',
        group: opts.group,
        startTime,
        endTime: fibEndTime
      };
      lines.push(rec);

      // Add list item for single lines, but not for individual fib levels
      if (opts.kind === 'single') {
        addListItem(rec);
      }

      post('lw_line_added', { price, kind: rec.kind, group: rec.group });
      return rec;
    }

    // Create a proper rectangle using LineSeries
    function createRectangle(startTime, endTime, topPrice, bottomPrice, opts = {}) {
      const rectSeries = chart.addLineSeries({
        color: opts.color || '#58a6ff',
        lineWidth: opts.lineWidth ?? 2,
        lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
        priceLineVisible: false,
        lastValueVisible: false,
        title: opts.title || 'Rectangle'
      });

      // Create rectangle outline data points
      const rectangleData = [
        { time: startTime, value: topPrice },    // Top-left
        { time: endTime, value: topPrice },      // Top-right
        { time: endTime, value: bottomPrice },   // Bottom-right
        { time: startTime, value: bottomPrice }, // Bottom-left
        { time: startTime, value: topPrice }     // Close the rectangle
      ];

      rectSeries.setData(rectangleData);

      const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const rec = {
        id,
        handle: rectSeries,
        kind: 'rectangle',
        group: opts.group,
        startTime,
        endTime,
        topPrice,
        bottomPrice
      };
      lines.push(rec);
      addListItem(rec);

      post('lw_line_added', { kind: rec.kind, group: rec.group });
      return rec;
    }

    function removeLineById(id) {
      const idx = lines.findIndex(l => l.id === id);
      if (idx === -1) return;
      const rec = lines[idx];

      // Handle different types of chart elements
      if (rec.handle) {
        // Check if it's a LineSeries-based element (all Fibonacci levels, rectangles, and single lines)
        const isLineSeries = rec.kind === 'fibLevel' || rec.kind === 'fib1' || rec.kind === 'fib2' ||
                            rec.kind === 'fib50' || rec.kind === 'fib618' || rec.kind === 'fib786' ||
                            rec.kind === 'fibtp236' || rec.kind === 'fibtp382' || rec.kind === 'single' ||
                            rec.kind === 'rectangle';

        if (isLineSeries) {
          // Remove LineSeries-based elements
          try { chart.removeSeries(rec.handle); } catch(e){}
        } else {
          // Remove price line (trendlines don't have handles, they're managed by group removal)
          try { series.removePriceLine(rec.handle); } catch(e){}
        }
      }

      // Handle trendline series removal
      if (rec.kind === 'trendline' && rec.trendlineSeries) {
        try { chart.removeSeries(rec.trendlineSeries); } catch(e){}
      }

      lines.splice(idx, 1);
      const el = document.getElementById('li-' + id);
      if (el) el.remove();
      post('lw_line_removed', { id, kind: rec.kind, group: rec.group });
    }

    function removeGroup(groupId) {
      const groupLines = lines.filter(l => l.group === groupId);

      // Check if this group contains a Fibonacci set
      const fibInGroup = groupLines.find(l => l.kind === 'fibset' ||
        l.kind === 'fib1' || l.kind === 'fib2' || l.kind === 'fib50' ||
        l.kind === 'fib618' || l.kind === 'fib786' || l.kind === 'fibtp236' || l.kind === 'fibtp382');

      if (fibInGroup) {
        // Clear trade details when removing a Fibonacci set
        tradeInfoEl.innerHTML = '<div class="muted">Draw a Fibonacci retracement to generate trade plan</div>';
      }

      // Handle rectangle markers removal
      const rectangleInGroup = groupLines.find(l => l.kind === 'rectangle');
      if (rectangleInGroup && rectangleInGroup.markers) {
        try {
          // Get existing markers and filter out the rectangle's markers
          const existingMarkers = series.markers?.() || [];
          const filteredMarkers = existingMarkers.filter(marker =>
            !rectangleInGroup.markers.some(rectMarker =>
              rectMarker.time === marker.time && rectMarker.text === marker.text
            )
          );
          series.setMarkers(filteredMarkers);
        } catch(e) {
          console.log('Error removing rectangle markers:', e);
        }
      }

      // Handle trendline series removal
      const trendlineInGroup = groupLines.find(l => l.kind === 'trendline');
      if (trendlineInGroup && trendlineInGroup.trendlineSeries) {
        try {
          chart.removeSeries(trendlineInGroup.trendlineSeries);
        } catch(e) {
          console.log('Error removing trendline series:', e);
        }
      }

      const toRemove = groupLines.map(l => l.id);
      toRemove.forEach(removeLineById);
    }

    function addListItem(rec) {
      const li = document.createElement('li');
      li.id = 'li-' + rec.id;

      const left = document.createElement('div');
      if (rec.kind === 'fibset') {
        left.textContent = 'Fib Set ';
        const pill = document.createElement('span');
        pill.className='pill';
        pill.textContent = `P1: ${fmt(rec.p1)} → P2: ${fmt(rec.p2)}`;
        left.appendChild(pill);
      } else if (rec.kind === 'rectangle') {
        left.textContent = 'Rectangle ';
        const pill = document.createElement('span');
        pill.className='pill';
        pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
        left.appendChild(pill);
      } else if (rec.kind === 'trendline') {
        left.textContent = 'Trendline ';
        const pill = document.createElement('span');
        pill.className='pill';
        pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
        left.appendChild(pill);
      } else {
        left.textContent = (rec.kind === 'single') ? 'Line @ ' : 'Line @ ';
        const pill = document.createElement('span'); pill.className='pill'; pill.textContent = fmt(rec.price);
        left.appendChild(pill);
      }

      if (rec.group && (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline')) {
        const tag = document.createElement('span');
        tag.className = 'groupTag';
        tag.textContent = rec.group.slice(0,6);
        left.appendChild(tag);
      }

      const right = document.createElement('div');

      if (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline') {
        // For fib sets, rectangles, and trendlines, only show delete button (no copy since it's multiple values)
        const del = document.createElement('button'); del.className='btn'; del.textContent='🗑️';
        del.onclick = () => removeGroup(rec.group);
        right.appendChild(del);
      } else {
        // For single lines, show both copy and delete
        const copy = document.createElement('button'); copy.className='btn'; copy.textContent='Copy';
        copy.onclick = () => { navigator.clipboard.writeText(String(rec.price)); copy.textContent='✔'; setTimeout(()=>copy.textContent='Copy',800); };

        const del = document.createElement('button'); del.className='btn'; del.textContent='🗑️';
        del.onclick = () => removeLineById(rec.id);

        right.append(copy, del);
      }

      li.append(left, right);
      listEl.appendChild(li);
    }

    function post(type, payload) {
      try { window.parent?.postMessage({ type, ...payload }, '*'); } catch(e){}
    }

    // Helper function to calculate trendline price at a given time
    function calculateTrendlinePrice(startTime, startPrice, endTime, endPrice, targetTime) {
      if (startTime === endTime) return startPrice; // Avoid division by zero
      const slope = (endPrice - startPrice) / (endTime - startTime);
      return startPrice + slope * (targetTime - startTime);
    }

    // Helper function to create trendline series
    function createTrendlineSeries(startPoint, endPoint, opts = {}) {
      // Validate input points
      if (!startPoint || !endPoint ||
          startPoint.time == null || endPoint.time == null ||
          startPoint.price == null || endPoint.price == null) {
        console.error('Invalid trendline points:', { startPoint, endPoint });
        return null;
      }

      const trendlineSeries = chart.addLineSeries({
        color: opts.color || '#ff6b6b',
        lineWidth: opts.lineWidth || 2,
        lineStyle: opts.lineStyle || 0, // solid
        lastValueVisible: false,
        priceLineVisible: false,
        title: opts.title || 'Trendline'
      });

      // Ensure times are valid numbers (Unix timestamps)
      const startTime = typeof startPoint.time === 'number' ? startPoint.time : Math.floor(startPoint.time);
      const endTime = typeof endPoint.time === 'number' ? endPoint.time : Math.floor(endPoint.time);

      // Create trendline data points including future projection
      const trendlineData = [
        { time: startTime, value: startPoint.price },
        { time: endTime, value: endPoint.price }
      ];

      // No automatic future projection - user controls trendline endpoints

      // Set the trendline data with future projection
      try {
        trendlineSeries.setData(trendlineData);
      } catch (error) {
        console.error('Error setting trendline data:', error, { startTime, endTime, startPrice: startPoint.price, endPrice: endPoint.price });
        chart.removeSeries(trendlineSeries);
        return null;
      }

      return trendlineSeries;
    }

    // Track last crosshair point so we always have exact y
    chart.subscribeCrosshairMove((param) => {
      if (param?.point && typeof param.point.y === 'number') {
        lastPoint = param.point;

        // live-update temp trendline (with throttling)
        if (trendlineActive && trendlineStartPoint) {
          const now = Date.now();

          // Throttle updates to prevent excessive calls (max 10 updates per second)
          if (now - lastTrendlineUpdate < 100) {
            return;
          }
          lastTrendlineUpdate = now;

          let currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
          const currentPrice = snap(series.coordinateToPrice(lastPoint.y));

          // Handle null currentTime in whitespace area
          if (currentTime == null) {
            const visibleRange = chart.timeScale().getVisibleRange();
            if (visibleRange) {
              const chartWidth = chartEl.clientWidth;
              const totalTimeRange = visibleRange.to - visibleRange.from;
              const mouseRatio = lastPoint.x / chartWidth;
              currentTime = visibleRange.from + (totalTimeRange * mouseRatio);
            }
          }

          console.log('Temp trendline coordinates:', { currentTime, currentPrice, startTime: trendlineStartPoint.time, startPrice: trendlineStartPoint.price });

          // Validate current coordinates and ensure different from start point
          if (currentTime != null && !isNaN(currentPrice) &&
              currentTime !== trendlineStartPoint.time &&
              Math.abs(currentPrice - trendlineStartPoint.price) > 0.001) {

            // Remove existing temp trendline
            if (tempTrendlineSeries) {
              try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
              tempTrendlineSeries = null;
            }

            // Create new temp trendline only if points are sufficiently different
            tempTrendlineSeries = createTrendlineSeries(
              { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
              { time: currentTime, price: currentPrice },
              { color: '#cccccc', lineWidth: 1, lineStyle: 2, title: 'Trendline?' }
            );
          }
        }

        // live-update temp fib end line
        if (fibActive) {
          const y = lastPoint.y;
          const price = snap(series.coordinateToPrice(y));
          // re-create dynamic end line
          if (tempEnd) { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd = null; }
          tempEnd = series.createPriceLine({
            price,
            color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P2?'
          });
        }

        // live-update temp rectangle (with throttling like trendline)
        if (rectActive) {
          const now = Date.now();
          const currentPrice = snap(series.coordinateToPrice(lastPoint.y));
          const topPrice = Math.max(rectStartPoint.price, currentPrice);
          const bottomPrice = Math.min(rectStartPoint.price, currentPrice);

          // Always update horizontal lines (lightweight like Fib)
          if (tempRectTop) { try { series.removePriceLine(tempRectTop); } catch(e){} tempRectTop = null; }
          if (tempRectBottom) { try { series.removePriceLine(tempRectBottom); } catch(e){} tempRectBottom = null; }

          tempRectTop = series.createPriceLine({
            price: topPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'Rect Top'
          });
          tempRectBottom = series.createPriceLine({
            price: bottomPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'Rect Bottom'
          });

          // Throttle left vertical line updates (like trendline throttling)
          if (tempRectLeft && now - lastRectUpdate > 50) { // Update every 50ms max
            lastRectUpdate = now;
            try {
              tempRectLeft.setData([
                { time: rectStartPoint.time, value: topPrice },
                { time: rectStartPoint.time, value: bottomPrice }
              ]);
            } catch(e) {
              console.log('Throttled left line update error:', e);
            }
          }

          // Schedule a final update for when mouse stops moving (debounced)
          if (rectUpdateTimeout) clearTimeout(rectUpdateTimeout);
          rectUpdateTimeout = setTimeout(() => {
            if (tempRectLeft && rectActive) {
              try {
                tempRectLeft.setData([
                  { time: rectStartPoint.time, value: topPrice },
                  { time: rectStartPoint.time, value: bottomPrice }
                ]);
              } catch(e) {
                console.log('Final rect update error:', e);
              }
            }
          }, 100); // Final update 100ms after mouse stops
        }
      }
    });

    // ----- Modes -----
    function setMode(newMode){
      mode = newMode;
      btnNormal.classList.toggle('active', mode==='normal');
      btnLine.classList.toggle('active', mode==='line');
      btnTrendline.classList.toggle('active', mode==='trendline');
      btnFib.classList.toggle('active',  mode==='fib');
      btnRect.classList.toggle('active', mode==='rect');

      // Enable/disable pan and zoom based on mode
      setPanZoomEnabled(mode === 'normal');

      hintEl.textContent = (mode==='normal')
          ? 'Mode: Normal — chart viewing mode with pan and zoom enabled.'
          : (mode==='line')
          ? 'Mode: Line — click anywhere to drop a horizontal line at the exact price.'
          : (mode==='trendline')
          ? 'Mode: Trendline — click and drag to draw a diagonal trendline. Useful for trend analysis and support/resistance levels.'
          : (mode==='rect')
          ? 'Mode: Rectangle — click and drag to draw a rectangle. Useful for highlighting price ranges or time periods.'
          : 'Mode: Fib — mouse down to set P1, drag, mouse up to set P2. Creates P1, P2, 50%, 61.8%, 78.6% retracements and -23.6%, -38.2% take profit levels. Shift+click trash on any fib level to delete the whole set.';
    }
    btnNormal.onclick = () => setMode('normal');
    btnLine.onclick = () => setMode('line');
    btnTrendline.onclick = () => setMode('trendline');
    btnFib.onclick  = () => setMode('fib');
    btnRect.onclick = () => setMode('rect');
    setMode('normal');

    // ----- Click → single line (only in Line mode) -----
    chart.subscribeClick((param) => {
      if (mode !== 'line') return;
      if (!param?.point || typeof param.point.y !== 'number') return;

      const price = snap(series.coordinateToPrice(param.point.y));
      const clickTime = chart.timeScale().coordinateToTime(param.point.x);

      if (clickTime == null) {
        console.error('Invalid time coordinate for line placement');
        return;
      }

      // Create time-bounded line starting from click point
      const currentData = series.data();
      const lastTime = currentData && currentData.length > 0 ?
        currentData[currentData.length - 1].time : clickTime;
      const lineEndTime = Math.max(clickTime, lastTime + (3600 * 24 * 7)); // Extend 7 days into future

      createFibLevel(price, clickTime, lineEndTime, {
        color:'#58a6ff',
        kind:'single',
        title:`Line ${price.toFixed(2)}`
      });

      console.log('Time-bounded line placed at', price, 'from time', clickTime);

      // Automatically switch back to normal mode after placing a line
      setMode('normal');
    });

    // ----- Trendline, Fib & Rectangle: mousedown starts drawing -----
    chartEl.addEventListener('mousedown', (e) => {
      if (mode === 'trendline') {
        if (!lastPoint || typeof lastPoint.y !== 'number') return;

        // Disable pan/zoom while dragging Trendline
        setPanZoomEnabled(false);

        const time = chart.timeScale().coordinateToTime(lastPoint.x);
        const price = snap(series.coordinateToPrice(lastPoint.y));

        // Validate coordinates
        if (time == null || isNaN(price)) {
          console.error('Invalid start coordinates for trendline:', { time, price });
          setPanZoomEnabled(true);
          return;
        }

        trendlineActive = true;
        trendlineStartPoint = { x: lastPoint.x, y: lastPoint.y, time, price };

        console.log('Trendline start point:', trendlineStartPoint);

        // prevent the chart from seeing this as a pan start
        e.preventDefault();
        e.stopPropagation();
      } else if (mode === 'fib') {
        if (!lastPoint || typeof lastPoint.y !== 'number') return;

        // Disable pan/zoom while dragging Fib
        setPanZoomEnabled(false);

        fibActive = true;
        fibStartPrice = snap(series.coordinateToPrice(lastPoint.y));
        fibStartTime = chart.timeScale().coordinateToTime(lastPoint.x); // capture start time

        // temp start line
        tempStart = series.createPriceLine({
          price: fibStartPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P1'
        });

        // prevent the chart from seeing this as a pan start
        e.preventDefault();
        e.stopPropagation();
      } else if (mode === 'rect') {
        if (!lastPoint || typeof lastPoint.y !== 'number') return;

        // Disable pan/zoom while dragging Rectangle
        setPanZoomEnabled(false);

        rectActive = true;
        const time = chart.timeScale().coordinateToTime(lastPoint.x);
        const price = snap(series.coordinateToPrice(lastPoint.y));
        rectStartPoint = { x: lastPoint.x, y: lastPoint.y, time, price };

        // Create initial temp lines + left vertical line
        tempRectTop = series.createPriceLine({
          price, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'Rect Start'
        });
        tempRectBottom = tempRectTop; // Start with same line, will split on mousemove

        // Create left vertical line (shows rectangle start)
        tempRectLeft = chart.addLineSeries({
          color: '#cccccc',
          lineWidth: 1,
          lineStyle: 2, // dashed
          priceLineVisible: false,
          lastValueVisible: false
        });

        // Set initial left line (just at start point)
        const currentData = series.data();
        if (currentData && currentData.length > 0) {
          const minPrice = Math.min(...currentData.map(d => d.low));
          const maxPrice = Math.max(...currentData.map(d => d.high));
          tempRectLeft.setData([
            { time, value: minPrice },
            { time, value: maxPrice }
          ]);
        }

        // prevent the chart from seeing this as a pan start
        e.preventDefault();
        e.stopPropagation();
      }
    });

    const endFib = () => {
      if (!fibActive) return;
      fibActive = false;

      // finalize P2
      if (!lastPoint || typeof lastPoint.y !== 'number') {
        // cleanup temps
        if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
        if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }
        return;
      }
      const fibEndPrice = snap(series.coordinateToPrice(lastPoint.y));
      const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

      // remove temps
      if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
      if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }

      // Get end time for time-bounded Fib levels
      const fibEndTime = chart.timeScale().coordinateToTime(lastPoint.x);

      // create time-bounded permanent lines
      const p1 = createFibLevel(fibStartPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'P1' });
      const p2 = createFibLevel(fibEndPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'P2' });

      // Calculate Fibonacci retracement levels (from high back toward low)
      const range = fibEndPrice - fibStartPrice;
      const fib50 = snap(fibStartPrice + range * 0.5);
      const fib618 = snap(fibEndPrice - range * 0.618); // 61.8% retracement from high
      const fib786 = snap(fibEndPrice - range * 0.786); // 78.6% retracement from high

      // Calculate take profit levels (extensions beyond P2)
      const fibtp236 = snap(fibEndPrice + range * 0.236);
      const fibtp382 = snap(fibEndPrice + range * 0.382); // Changed from 61.8% to 38.2%

      // Create time-bounded retracement lines
      const p50 = createFibLevel(fib50, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib50', group, title:'50%' });
      const p618 = createFibLevel(fib618, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib618', group, title:'61.8%' });
      const p786 = createFibLevel(fib786, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib786', group, title:'78.6%' });

      // Create time-bounded take profit lines
      const ptp236 = createFibLevel(fibtp236, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp236', group, title:'TP -23.6%' });
      const ptp382 = createFibLevel(fibtp382, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp382', group, title:'TP -38.2%' });

      // Create a single list entry for the entire Fibonacci set
      const fibSetId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const fibSetRec = { id: fibSetId, p1: fibStartPrice, p2: fibEndPrice, kind: 'fibset', group, handle: null };
      lines.push(fibSetRec);
      addListItem(fibSetRec);

      console.log('Fib set: P1=', fibStartPrice, ' P2=', fibEndPrice, ' levels=', { fib50, fib618, fib786, fibtp236, fibtp382 });
      post('lw_fib_added', { p1:fibStartPrice, p2:fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp382 });

      // Generate trade details
      console.log('Calling generateTradeDetails for normal fib with:', { fibStartPrice, fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp382 });
      generateTradeDetails(fibStartPrice, fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp382);

      fibStartPrice = null;
      fibStartTime = null;

      // Automatically switch back to normal mode after completing Fibonacci retracement
      setMode('normal');
    };

    // mouseup on whole doc so it completes even if cursor leaves chart
    document.addEventListener('mouseup', () => {
      if (!trendlineActive && !fibActive && !rectActive) return;

      // Re-enable pan/zoom now that we’re done
      setPanZoomEnabled(true);

      if (trendlineActive) {
        trendlineActive = false;

        if (!lastPoint || typeof lastPoint.y !== 'number' || !trendlineStartPoint) {
          // cleanup temp trendline
          if (tempTrendlineSeries) {
            try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
            tempTrendlineSeries = null;
          }
          trendlineStartPoint = null;
          return;
        }

        let currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
        const currentPrice = snap(series.coordinateToPrice(lastPoint.y));

        // Handle null currentTime in whitespace area
        if (currentTime == null) {
          const visibleRange = chart.timeScale().getVisibleRange();
          if (visibleRange) {
            const chartWidth = chartEl.clientWidth;
            const totalTimeRange = visibleRange.to - visibleRange.from;
            const mouseRatio = lastPoint.x / chartWidth;
            currentTime = visibleRange.from + (totalTimeRange * mouseRatio);

            console.log('Calculated future time for trendline completion:', {
              currentTime,
              futureDate: new Date(currentTime * 1000).toISOString(),
              mouseX: lastPoint.x,
              chartWidth
            });
          }
        }

        // Validate coordinates before proceeding
        if (currentTime == null || isNaN(currentPrice)) {
          console.error('Invalid coordinates for trendline completion:', { currentTime, currentPrice });
          if (tempTrendlineSeries) {
            try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
            tempTrendlineSeries = null;
          }
          trendlineStartPoint = null;
          return;
        }

        // Check if start and end points are too similar (prevent invalid trendlines)
        if (currentTime === trendlineStartPoint.time ||
            Math.abs(currentPrice - trendlineStartPoint.price) < 0.001) {
          console.log('Trendline points too similar, canceling:', {
            startTime: trendlineStartPoint.time,
            endTime: currentTime,
            startPrice: trendlineStartPoint.price,
            endPrice: currentPrice
          });
          if (tempTrendlineSeries) {
            try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
            tempTrendlineSeries = null;
          }
          trendlineStartPoint = null;
          return;
        }

        const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

        // Remove temp trendline
        if (tempTrendlineSeries) {
          try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
          tempTrendlineSeries = null;
        }

        // Create permanent trendline
        const permanentTrendlineSeries = createTrendlineSeries(
          { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
          { time: currentTime, price: currentPrice },
          { color: '#ff6b6b', lineWidth: 2, title: 'Trendline' }
        );

        // Check if trendline creation was successful
        if (!permanentTrendlineSeries) {
          console.error('Failed to create permanent trendline series');
          trendlineStartPoint = null;
          return;
        }

        // User controls trendline endpoints - no automatic extension

        // Create a single list entry for the trendline
        const trendlineId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
        const trendlineRec = {
          id: trendlineId,
          p1: trendlineStartPoint.price,
          p2: currentPrice,
          t1: trendlineStartPoint.time,
          t2: currentTime,
          kind: 'trendline',
          group,
          handle: null,
          trendlineSeries: permanentTrendlineSeries
        };
        lines.push(trendlineRec);
        addListItem(trendlineRec);

        console.log('Trendline created:', {
          p1: trendlineStartPoint.price,
          p2: currentPrice,
          t1: trendlineStartPoint.time,
          t2: currentTime,
          // Calculate current price intersection for demonstration
          currentIntersection: calculateTrendlinePrice(
            trendlineStartPoint.time,
            trendlineStartPoint.price,
            currentTime,
            currentPrice,
            Math.floor(Date.now() / 1000) // current timestamp
          )
        });

        post('lw_trendline_added', {
          p1: trendlineStartPoint.price,
          p2: currentPrice,
          t1: trendlineStartPoint.time,
          t2: currentTime,
          group
        });

        trendlineStartPoint = null;

        // Automatically switch back to normal mode after completing trendline
        setMode('normal');
      }

      if (fibActive) {
        endFib(); // Use the existing endFib function instead of duplicating code
      } else if (rectActive) {
        rectActive = false;

        if (!lastPoint || typeof lastPoint.y !== 'number' || !rectStartPoint) {
          // cleanup temp rectangle lines (like Fib cleanup + left line)
          if (tempRectTop) { try { series.removePriceLine(tempRectTop); } catch(e){} tempRectTop = null; }
          if (tempRectBottom && tempRectBottom !== tempRectTop) { try { series.removePriceLine(tempRectBottom); } catch(e){} tempRectBottom = null; }
          if (tempRectLeft) { try { chart.removeSeries(tempRectLeft); } catch(e){} tempRectLeft = null; }
          if (rectUpdateTimeout) { clearTimeout(rectUpdateTimeout); rectUpdateTimeout = null; }
          rectStartPoint = null;
          return;
        }

        const currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
        const currentPrice = snap(series.coordinateToPrice(lastPoint.y));
        const topPrice = Math.max(rectStartPoint.price, currentPrice);
        const bottomPrice = Math.min(rectStartPoint.price, currentPrice);
        const leftTime = Math.min(rectStartPoint.time, currentTime);
        const rightTime = Math.max(rectStartPoint.time, currentTime);
        const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

        // Remove temp rectangle lines (like Fib cleanup + left line)
        if (tempRectTop) { try { series.removePriceLine(tempRectTop); } catch(e){} tempRectTop = null; }
        if (tempRectBottom && tempRectBottom !== tempRectTop) { try { series.removePriceLine(tempRectBottom); } catch(e){} tempRectBottom = null; }
        if (tempRectLeft) { try { chart.removeSeries(tempRectLeft); } catch(e){} tempRectLeft = null; }
        if (rectUpdateTimeout) { clearTimeout(rectUpdateTimeout); rectUpdateTimeout = null; }

        // Create proper rectangle using LineSeries
        createRectangle(leftTime, rightTime, topPrice, bottomPrice, {
          color: '#58a6ff',
          lineWidth: 2,
          group,
          title: `Rectangle ${topPrice.toFixed(2)}-${bottomPrice.toFixed(2)}`
        });

        console.log('Rectangle created:', {
          startTime: leftTime,
          endTime: rightTime,
          topPrice,
          bottomPrice
        });

        rectStartPoint = null;

        // Automatically switch back to normal mode after completing rectangle
        setMode('normal');
      }
    });

    // ----- Auto-Fib button handler -----
    btnAutoFib.onclick = () => {
      const currentData = series.data();
      if (!currentData || currentData.length < 10) {
        console.log('Not enough data for Auto-Fib analysis');
        return;
      }

      // Filter out whitespace points for analysis
      const realData = currentData.filter(candle => candle.open !== undefined);

      const swing = findMostRecentSwing(realData);
      if (!swing) {
        console.log('No valid swing found for Auto-Fib');
        return;
      }

      console.log('Auto-Fib detected swing:', swing);

      // Create Auto-Fib using time-bounded levels
      const fibStartPrice = swing.from.price;
      const fibEndPrice = swing.to.price;
      const fibStartTime = swing.from.time;
      const fibEndTime = swing.to.time;
      const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

      // Create time-bounded P1 and P2 lines
      const p1 = createFibLevel(fibStartPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'Auto P1' });
      const p2 = createFibLevel(fibEndPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'Auto P2' });

      // Calculate Fibonacci retracement levels (from high back toward low)
      const range = fibEndPrice - fibStartPrice;
      const fib50 = snap(fibStartPrice + range * 0.5);
      const fib618 = snap(fibEndPrice - range * 0.618); // 61.8% retracement from high
      const fib786 = snap(fibEndPrice - range * 0.786); // 78.6% retracement from high

      // Calculate take profit levels (extensions beyond P2)
      const fibtp236 = snap(fibEndPrice + range * 0.236);
      const fibtp382 = snap(fibEndPrice + range * 0.382);

      // Create time-bounded retracement lines (start from P1 time)
      const p50 = createFibLevel(fib50, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib50', group, title:'Auto 50%' });
      const p618 = createFibLevel(fib618, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib618', group, title:'Auto 61.8%' });
      const p786 = createFibLevel(fib786, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib786', group, title:'Auto 78.6%' });

      // Create time-bounded take profit lines (start from P1 time)
      const ptp236 = createFibLevel(fibtp236, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp236', group, title:'Auto TP -23.6%' });
      const ptp382 = createFibLevel(fibtp382, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp382', group, title:'Auto TP -38.2%' });

      // Create a single list entry for the Auto-Fib set
      const autoFibSetId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const autoFibSetRec = {
        id: autoFibSetId,
        p1: fibStartPrice,
        p2: fibEndPrice,
        kind: 'fibset',
        group,
        handle: null,
        swingType: swing.type
      };
      lines.push(autoFibSetRec);
      addListItem(autoFibSetRec);

      console.log(`Auto-Fib created: ${swing.type} swing from ${fibStartPrice.toFixed(2)} to ${fibEndPrice.toFixed(2)}`);

      // Generate trade details for Auto-Fib
      generateTradeDetails(fibStartPrice, fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp382);
    };

    // ----- Clear all -----
    btnClear.onclick = () => {
      // remove all lines & list
      for (const l of [...lines]) {
        // Check if it's a LineSeries-based element (all Fibonacci levels, rectangles, and single lines)
        const isLineSeries = l.kind === 'fibLevel' || l.kind === 'fib1' || l.kind === 'fib2' ||
                            l.kind === 'fib50' || l.kind === 'fib618' || l.kind === 'fib786' ||
                            l.kind === 'fibtp236' || l.kind === 'fibtp382' || l.kind === 'single' ||
                            l.kind === 'rectangle';

        if (isLineSeries) {
          try { chart.removeSeries(l.handle); } catch(e){}
        } else {
          try { series.removePriceLine(l.handle); } catch(e){}
        }
      }
      lines.length = 0;
      listEl.innerHTML = '';

      // Clear trade details
      tradeInfoEl.innerHTML = '<div class="muted">Draw a Fibonacci retracement to generate trade plan</div>';

      post('lw_lines_cleared', {});
    };

    // ----- Drawings panel toggle -----
    btnDrawingsToggle.onclick = () => {
      drawingsPanel.classList.toggle('show');
    };

    // Close drawings panel when clicking outside
    document.addEventListener('click', (e) => {
      if (!drawingsPanel.contains(e.target) && !btnDrawingsToggle.contains(e.target)) {
        drawingsPanel.classList.remove('show');
      }
    });

    // ----- Trade Details Generation -----
    function generateTradeDetails(p1, p2, fib50, fib618, fib786, fibtp236, fibtp382) {
      console.log('generateTradeDetails called with:', { p1, p2, fib50, fib618, fib786, fibtp236, fibtp382 });
      console.log('tradeInfoEl:', tradeInfoEl);

      if (!tradeInfoEl) {
        console.error('tradeInfoEl not found!');
        return;
      }

      // Clear any existing trade details first
      tradeInfoEl.innerHTML = '<div class="muted">Generating trade plan...</div>';

      // Determine if P1 is low and P2 is high (bullish setup)
      const isLowToHigh = p1 < p2;
      const accountSize = 10000;

      if (isLowToHigh) {
        // LONG: P1 (low) = Stop Loss, retracements = entries, extensions = take profits
        const stopLoss = p1;
        const entries = [
          { level: '50%', price: fib50, qty: 2 },
          { level: '61.8%', price: fib618, qty: 2 },
          { level: '78.6%', price: fib786, qty: 2 }
        ];
        const takeProfits = [
          { level: '23.6%', price: fibtp236, qty: 3, note: 'Half position' },
          { level: '38.2%', price: fibtp382, qty: 3, note: 'Remaining position' }
        ];

        displayTradeDetails('LONG', stopLoss, entries, takeProfits, accountSize);
      } else {
        // SHORT: P1 (high) = Stop Loss, retracements = entries, extensions = take profits
        const stopLoss = p1;
        const entries = [
          { level: '50%', price: fib50, qty: 2 },
          { level: '61.8%', price: fib618, qty: 2 },
          { level: '78.6%', price: fib786, qty: 2 }
        ];
        const takeProfits = [
          { level: '23.6%', price: fibtp236, qty: 3, note: 'Half position' },
          { level: '38.2%', price: fibtp382, qty: 3, note: 'Remaining position' }
        ];

        displayTradeDetails('SHORT', stopLoss, entries, takeProfits, accountSize);
      }
    }

    function displayTradeDetails(direction, stopLoss, entries, takeProfits, accountSize) {
      // Calculate total position size and risk
      const totalQty = entries.reduce((sum, entry) => sum + entry.qty, 0);
      const avgEntryPrice = entries.reduce((sum, entry) => sum + (entry.price * entry.qty), 0) / totalQty;

      // Calculate risk per share and total risk
      const riskPerShare = Math.abs(avgEntryPrice - stopLoss);
      const totalRisk = riskPerShare * totalQty;
      const accountRisk = (totalRisk / accountSize) * 100;

      // Calculate potential profits
      const tp1Profit = Math.abs(takeProfits[0].price - avgEntryPrice) * takeProfits[0].qty;
      const tp2Profit = Math.abs(takeProfits[1].price - avgEntryPrice) * takeProfits[1].qty;
      const totalPotentialProfit = tp1Profit + tp2Profit;
      const riskRewardRatio = totalPotentialProfit / totalRisk;

      let html = `<div style="font-weight: bold; margin-bottom: 8px; color: ${direction === 'LONG' ? '#28a745' : '#dc3545'};">${direction} Trade Plan</div>`;

      // Risk Summary
      html += `<div style="background: #1a1a1a; border: 1px solid #333; border-radius: 4px; padding: 6px; margin-bottom: 8px; font-size: 11px;">
        <div><strong>Account Risk:</strong> ${accountRisk.toFixed(2)}% ($${totalRisk.toFixed(2)})</div>
        <div><strong>Risk/Reward:</strong> 1:${riskRewardRatio.toFixed(2)}</div>
        <div><strong>Potential Profit:</strong> $${totalPotentialProfit.toFixed(2)}</div>
      </div>`;

      // Stop Loss
      html += `<div class="trade-entry stop-loss">
        <strong>Stop Loss:</strong> ${stopLoss.toFixed(2)} <span style="font-size: 10px;">(Risk: $${totalRisk.toFixed(2)})</span>
      </div>`;

      // Entries
      html += `<div style="margin: 8px 0 4px 0; font-weight: bold; font-size: 12px;">Entries (Total Qty: ${totalQty}):</div>`;
      entries.forEach(entry => {
        const entryValue = entry.price * entry.qty;
        html += `<div class="trade-entry entry">
          ${entry.level}: ${entry.price.toFixed(2)} (Qty: ${entry.qty}) <span style="font-size: 10px;">$${entryValue.toFixed(2)}</span>
        </div>`;
      });

      // Take Profits
      html += `<div style="margin: 8px 0 4px 0; font-weight: bold; font-size: 12px;">Take Profits:</div>`;
      takeProfits.forEach((tp, index) => {
        const profit = Math.abs(tp.price - avgEntryPrice) * tp.qty;
        html += `<div class="trade-entry take-profit">
          ${tp.level}: ${tp.price.toFixed(2)} (Qty: ${tp.qty}) <span style="font-size: 10px;">+$${profit.toFixed(2)}</span><br>
          <span style="font-size: 10px; opacity: 0.8;">${tp.note}</span>
        </div>`;
      });

      tradeInfoEl.innerHTML = html;
    }

    // ----- Auto-Fib: Market Structure Analysis -----
    function findSwingPoints(data) {
      if (!data || data.length < 5) return { swingHighs: [], swingLows: [] };

      const swingHighs = [];
      const swingLows = [];
      const lookback = 2; // Look 2 candles back and forward for swing confirmation

      // Find local swing highs and lows
      for (let i = lookback; i < data.length - lookback; i++) {
        const candle = data[i];
        if (!candle.high || !candle.low || !candle.close) continue;

        // Check for swing high (higher than surrounding candles)
        let isSwingHigh = true;
        for (let j = i - lookback; j <= i + lookback; j++) {
          if (j !== i && data[j] && data[j].high >= candle.high) {
            isSwingHigh = false;
            break;
          }
        }

        if (isSwingHigh) {
          swingHighs.push({
            index: i,
            time: candle.time,
            price: candle.high,
            type: 'high'
          });
        }

        // Check for swing low (lower than surrounding candles)
        let isSwingLow = true;
        for (let j = i - lookback; j <= i + lookback; j++) {
          if (j !== i && data[j] && data[j].low <= candle.low) {
            isSwingLow = false;
            break;
          }
        }

        if (isSwingLow) {
          swingLows.push({
            index: i,
            time: candle.time,
            price: candle.low,
            type: 'low'
          });
        }
      }

      console.log('Found swing points:', {
        highs: swingHighs.length,
        lows: swingLows.length,
        swingHighs: swingHighs.map(h => h.price.toFixed(2)),
        swingLows: swingLows.map(l => l.price.toFixed(2))
      });

      return { swingHighs, swingLows };
    }

    function findMostRecentSwing(data) {
      const { swingHighs, swingLows } = findSwingPoints(data);

      if (swingHighs.length === 0 || swingLows.length === 0) {
        console.log('Not enough swing points found');
        return null;
      }

      // Get the most recent swing high and low
      const lastHigh = swingHighs[swingHighs.length - 1];
      const lastLow = swingLows[swingLows.length - 1];

      console.log('Last swing high:', lastHigh.price.toFixed(2), 'at index', lastHigh.index);
      console.log('Last swing low:', lastLow.price.toFixed(2), 'at index', lastLow.index);

      // Find the most recent significant swing
      if (lastHigh.index > lastLow.index) {
        // Most recent swing is a high, find the previous low
        const previousLow = swingLows.reverse().find(low => low.index < lastHigh.index);
        swingLows.reverse(); // restore original order

        if (previousLow) {
          console.log('Bull swing detected:', previousLow.price.toFixed(2), 'to', lastHigh.price.toFixed(2));
          return {
            type: 'bull',
            from: previousLow,
            to: lastHigh
          };
        }
      } else {
        // Most recent swing is a low, find the previous high
        const previousHigh = swingHighs.reverse().find(high => high.index < lastLow.index);
        swingHighs.reverse(); // restore original order

        if (previousHigh) {
          console.log('Bear swing detected:', previousHigh.price.toFixed(2), 'to', lastLow.price.toFixed(2));
          return {
            type: 'bear',
            from: previousHigh,
            to: lastLow
          };
        }
      }

      console.log('No valid swing pattern found');
      return null;
    }

    // ----- Expose functions to external sources -----
    window.updateChartData = function(newData) {
      if (newData && Array.isArray(newData)) {
        series.setData(newData);
      }
    };



    // ----- Responsive -----
    const ro = new ResizeObserver(entries=>{
      const { width, height } = entries[0].contentRect;
      chart.applyOptions({ width, height });
    });
    ro.observe(chartEl);
  })();
  </script>
</body>
</html>
