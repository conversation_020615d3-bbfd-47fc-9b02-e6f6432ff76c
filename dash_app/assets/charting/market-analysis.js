// This file contains Auto-Fib swing detection and market structure analysis

// ----- Auto-Fib: Market Structure Analysis -----
function findSwingPoints(data) {
  if (!data || data.length < 5) return { swingHighs: [], swingLows: [] };

  const swingHighs = [];
  const swingLows = [];
  const lookback = 2; // Look 2 candles back and forward for swing confirmation

  // Find local swing highs and lows
  for (let i = lookback; i < data.length - lookback; i++) {
    const candle = data[i];
    if (!candle.high || !candle.low || !candle.close) continue;

    // Check for swing high (higher than surrounding candles)
    let isSwingHigh = true;
    for (let j = i - lookback; j <= i + lookback; j++) {
      if (j !== i && data[j] && data[j].high >= candle.high) {
        isSwingHigh = false;
        break;
      }
    }

    if (isSwingHigh) {
      swingHighs.push({
        index: i,
        time: candle.time,
        price: candle.high,
        type: 'high'
      });
    }

    // Check for swing low (lower than surrounding candles)
    let isSwingLow = true;
    for (let j = i - lookback; j <= i + lookback; j++) {
      if (j !== i && data[j] && data[j].low <= candle.low) {
        isSwingLow = false;
        break;
      }
    }

    if (isSwingLow) {
      swingLows.push({
        index: i,
        time: candle.time,
        price: candle.low,
        type: 'low'
      });
    }
  }

  console.log('Found swing points:', {
    highs: swingHighs.length,
    lows: swingLows.length,
    swingHighs: swingHighs.map(h => h.price.toFixed(2)),
    swingLows: swingLows.map(l => l.price.toFixed(2))
  });

  return { swingHighs, swingLows };
}

function findMostRecentSwing(data) {
  const { swingHighs, swingLows } = findSwingPoints(data);

  if (swingHighs.length === 0 || swingLows.length === 0) {
    console.log('Not enough swing points found');
    return null;
  }

  // Get the most recent swing high and low
  const lastHigh = swingHighs[swingHighs.length - 1];
  const lastLow = swingLows[swingLows.length - 1];

  console.log('Last swing high:', lastHigh.price.toFixed(2), 'at index', lastHigh.index);
  console.log('Last swing low:', lastLow.price.toFixed(2), 'at index', lastLow.index);

  // Find the most recent significant swing
  if (lastHigh.index > lastLow.index) {
    // Most recent swing is a high, find the previous low
    const previousLow = swingLows.reverse().find(low => low.index < lastHigh.index);
    swingLows.reverse(); // restore original order

    if (previousLow) {
      console.log('Bull swing detected:', previousLow.price.toFixed(2), 'to', lastHigh.price.toFixed(2));
      return {
        type: 'bull',
        from: previousLow,
        to: lastHigh
      };
    }
  } else {
    // Most recent swing is a low, find the previous high
    const previousHigh = swingHighs.reverse().find(high => high.index < lastLow.index);
    swingHighs.reverse(); // restore original order

    if (previousHigh) {
      console.log('Bear swing detected:', previousHigh.price.toFixed(2), 'to', lastLow.price.toFixed(2));
      return {
        type: 'bear',
        from: previousHigh,
        to: lastLow
      };
    }
  }

  console.log('No valid swing pattern found');
  return null;
}
