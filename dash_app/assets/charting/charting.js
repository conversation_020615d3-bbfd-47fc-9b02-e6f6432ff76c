// Main charting coordination
// This file contains crosshair tracking and event coordination

// Track last crosshair point so we always have exact y
chart.subscribeCrosshairMove((param) => {
  if (param?.point && typeof param.point.y === 'number') {
    lastPoint = param.point;

    // live-update temp trendline (with throttling)
    if (trendlineActive && trendlineStartPoint) {
      const now = Date.now();

      // Throttle updates to prevent excessive calls (max 10 updates per second)
      if (now - lastTrendlineUpdate < 100) {
        return;
      }
      lastTrendlineUpdate = now;

      let currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
      const currentPrice = snap(series.coordinateToPrice(lastPoint.y));

      // Handle null currentTime in whitespace area
      if (currentTime == null) {
        const visibleRange = chart.timeScale().getVisibleRange();
        if (visibleRange) {
          const chartWidth = chartEl.clientWidth;
          const totalTimeRange = visibleRange.to - visibleRange.from;
          const mouseRatio = lastPoint.x / chartWidth;
          currentTime = visibleRange.from + (totalTimeRange * mouseRatio);
        }
      }

      console.log('Temp trendline coordinates:', { currentTime, currentPrice, startTime: trendlineStartPoint.time, startPrice: trendlineStartPoint.price });

      // Validate current coordinates and ensure different from start point
      if (currentTime != null && !isNaN(currentPrice) &&
          currentTime !== trendlineStartPoint.time &&
          Math.abs(currentPrice - trendlineStartPoint.price) > 0.001) {

        // Remove existing temp trendline
        if (tempTrendlineSeries) {
          try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
          tempTrendlineSeries = null;
        }

        // Create new temp trendline only if points are sufficiently different
        tempTrendlineSeries = createTrendlineSeries(
          { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
          { time: currentTime, price: currentPrice },
          { color: '#cccccc', lineWidth: 1, lineStyle: 2, title: 'Trendline?' }
        );
      }
    }

    // live-update temp fib end line
    if (fibActive) {
      const y = lastPoint.y;
      const price = snap(series.coordinateToPrice(y));
      // re-create dynamic end line
      if (tempEnd) { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd = null; }
      tempEnd = series.createPriceLine({
        price,
        color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P2?'
      });
    }

    // live-update temp rectangle (with throttling like trendline)
    if (rectActive) {
      const now = Date.now();
      const currentPrice = snap(series.coordinateToPrice(lastPoint.y));
      const topPrice = Math.max(rectStartPoint.price, currentPrice);
      const bottomPrice = Math.min(rectStartPoint.price, currentPrice);

      // Always update horizontal lines (lightweight like Fib)
      if (tempRectTop) { try { series.removePriceLine(tempRectTop); } catch(e){} tempRectTop = null; }
      if (tempRectBottom) { try { series.removePriceLine(tempRectBottom); } catch(e){} tempRectBottom = null; }

      tempRectTop = series.createPriceLine({
        price: topPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'Rect Top'
      });
      tempRectBottom = series.createPriceLine({
        price: bottomPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'Rect Bottom'
      });

      // Throttle left vertical line updates (like trendline throttling)
      if (tempRectLeft && now - lastRectUpdate > 50) { // Update every 50ms max
        lastRectUpdate = now;
        try {
          tempRectLeft.setData([
            { time: rectStartPoint.time, value: topPrice },
            { time: rectStartPoint.time, value: bottomPrice }
          ]);
        } catch(e) {
          console.log('Throttled left line update error:', e);
        }
      }

      // Schedule a final update for when mouse stops moving (debounced)
      if (rectUpdateTimeout) clearTimeout(rectUpdateTimeout);
      rectUpdateTimeout = setTimeout(() => {
        if (tempRectLeft && rectActive) {
          try {
            tempRectLeft.setData([
              { time: rectStartPoint.time, value: topPrice },
              { time: rectStartPoint.time, value: bottomPrice }
            ]);
          } catch(e) {
            console.log('Final rect update error:', e);
          }
        }
      }, 100); // Final update 100ms after mouse stops
    }
  }
});

// mouseup on whole doc so it completes even if cursor leaves chart
document.addEventListener('mouseup', () => {
  if (!trendlineActive && !fibActive && !rectActive) return;

  // Re-enable pan/zoom now that we're done
  setPanZoomEnabled(true);

  if (trendlineActive) {
    trendlineActive = false;

    if (!lastPoint || typeof lastPoint.y !== 'number' || !trendlineStartPoint) {
      // cleanup temp trendline
      if (tempTrendlineSeries) {
        try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
        tempTrendlineSeries = null;
      }
      trendlineStartPoint = null;
      return;
    }

    let currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
    const currentPrice = snap(series.coordinateToPrice(lastPoint.y));

    // Handle null currentTime in whitespace area
    if (currentTime == null) {
      const visibleRange = chart.timeScale().getVisibleRange();
      if (visibleRange) {
        const chartWidth = chartEl.clientWidth;
        const totalTimeRange = visibleRange.to - visibleRange.from;
        const mouseRatio = lastPoint.x / chartWidth;
        currentTime = visibleRange.from + (totalTimeRange * mouseRatio);

        console.log('Calculated future time for trendline completion:', {
          currentTime,
          futureDate: new Date(currentTime * 1000).toISOString(),
          mouseX: lastPoint.x,
          chartWidth
        });
      }
    }

    // Validate coordinates before proceeding
    if (currentTime == null || isNaN(currentPrice)) {
      console.error('Invalid coordinates for trendline completion:', { currentTime, currentPrice });
      if (tempTrendlineSeries) {
        try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
        tempTrendlineSeries = null;
      }
      trendlineStartPoint = null;
      return;
    }

    // Check if start and end points are too similar (prevent invalid trendlines)
    if (currentTime === trendlineStartPoint.time ||
        Math.abs(currentPrice - trendlineStartPoint.price) < 0.001) {
      console.log('Trendline points too similar, canceling:', {
        startTime: trendlineStartPoint.time,
        endTime: currentTime,
        startPrice: trendlineStartPoint.price,
        endPrice: currentPrice
      });
      if (tempTrendlineSeries) {
        try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
        tempTrendlineSeries = null;
      }
      trendlineStartPoint = null;
      return;
    }

    const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

    // Remove temp trendline
    if (tempTrendlineSeries) {
      try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
      tempTrendlineSeries = null;
    }

    // Create permanent trendline
    const permanentTrendlineSeries = createTrendlineSeries(
      { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
      { time: currentTime, price: currentPrice },
      { color: '#ff6b6b', lineWidth: 2, title: 'Trendline' }
    );

    // Check if trendline creation was successful
    if (!permanentTrendlineSeries) {
      console.error('Failed to create permanent trendline series');
      trendlineStartPoint = null;
      return;
    }

    // User controls trendline endpoints - no automatic extension

    // Create a single list entry for the trendline
    const trendlineId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
    const trendlineRec = {
      id: trendlineId,
      p1: trendlineStartPoint.price,
      p2: currentPrice,
      t1: trendlineStartPoint.time,
      t2: currentTime,
      kind: 'trendline',
      group,
      handle: null,
      trendlineSeries: permanentTrendlineSeries
    };
    lines.push(trendlineRec);
    addListItem(trendlineRec);

    console.log('Trendline created:', {
      p1: trendlineStartPoint.price,
      p2: currentPrice,
      t1: trendlineStartPoint.time,
      t2: currentTime,
      // Calculate current price intersection for demonstration
      currentIntersection: calculateTrendlinePrice(
        trendlineStartPoint.time,
        trendlineStartPoint.price,
        currentTime,
        currentPrice,
        Math.floor(Date.now() / 1000) // current timestamp
      )
    });

    post('lw_trendline_added', {
      p1: trendlineStartPoint.price,
      p2: currentPrice,
      t1: trendlineStartPoint.time,
      t2: currentTime,
      group
    });

    trendlineStartPoint = null;

    // Automatically switch back to normal mode after completing trendline
    setMode('normal');
  }

  if (fibActive) {
    endFib(); // Use the existing endFib function instead of duplicating code
  } else if (rectActive) {
    rectActive = false;

    if (!lastPoint || typeof lastPoint.y !== 'number' || !rectStartPoint) {
      // cleanup temp rectangle lines (like Fib cleanup + left line)
      if (tempRectTop) { try { series.removePriceLine(tempRectTop); } catch(e){} tempRectTop = null; }
      if (tempRectBottom && tempRectBottom !== tempRectTop) { try { series.removePriceLine(tempRectBottom); } catch(e){} tempRectBottom = null; }
      if (tempRectLeft) { try { chart.removeSeries(tempRectLeft); } catch(e){} tempRectLeft = null; }
      if (rectUpdateTimeout) { clearTimeout(rectUpdateTimeout); rectUpdateTimeout = null; }
      rectStartPoint = null;
      return;
    }

    const currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
    const currentPrice = snap(series.coordinateToPrice(lastPoint.y));
    const topPrice = Math.max(rectStartPoint.price, currentPrice);
    const bottomPrice = Math.min(rectStartPoint.price, currentPrice);
    const leftTime = Math.min(rectStartPoint.time, currentTime);
    const rightTime = Math.max(rectStartPoint.time, currentTime);
    const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

    // Remove temp rectangle lines (like Fib cleanup + left line)
    if (tempRectTop) { try { series.removePriceLine(tempRectTop); } catch(e){} tempRectTop = null; }
    if (tempRectBottom && tempRectBottom !== tempRectTop) { try { series.removePriceLine(tempRectBottom); } catch(e){} tempRectBottom = null; }
    if (tempRectLeft) { try { chart.removeSeries(tempRectLeft); } catch(e){} tempRectLeft = null; }
    if (rectUpdateTimeout) { clearTimeout(rectUpdateTimeout); rectUpdateTimeout = null; }

    // Create proper rectangle using LineSeries
    createRectangle(leftTime, rightTime, topPrice, bottomPrice, {
      color: '#58a6ff',
      lineWidth: 2,
      group,
      title: `Rectangle ${topPrice.toFixed(2)}-${bottomPrice.toFixed(2)}`
    });

    console.log('Rectangle created:', {
      startTime: leftTime,
      endTime: rightTime,
      topPrice,
      bottomPrice
    });

    rectStartPoint = null;

    // Automatically switch back to normal mode after completing rectangle
    setMode('normal');
  }
});

// ----- Auto-Fib button handler -----
btnAutoFib.onclick = () => {
  const currentData = series.data();
  if (!currentData || currentData.length < 10) {
    console.log('Not enough data for Auto-Fib analysis');
    return;
  }

  // Filter out whitespace points for analysis
  const realData = currentData.filter(candle => candle.open !== undefined);

  const swing = findMostRecentSwing(realData);
  if (!swing) {
    console.log('No valid swing found for Auto-Fib');
    return;
  }

  console.log('Auto-Fib detected swing:', swing);

  // Create Auto-Fib using time-bounded levels
  const fibStartPrice = swing.from.price;
  const fibEndPrice = swing.to.price;
  const fibStartTime = swing.from.time;
  const fibEndTime = swing.to.time;
  const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

  // Create time-bounded P1 and P2 lines
  const p1 = createFibLevel(fibStartPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'Auto P1' });
  const p2 = createFibLevel(fibEndPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'Auto P2' });

  // Calculate Fibonacci retracement levels (from high back toward low)
  const range = fibEndPrice - fibStartPrice;
  const fib50 = snap(fibStartPrice + range * 0.5);
  const fib618 = snap(fibEndPrice - range * 0.618); // 61.8% retracement from high
  const fib786 = snap(fibEndPrice - range * 0.786); // 78.6% retracement from high

  // Calculate take profit levels (extensions beyond P2)
  const fibtp236 = snap(fibEndPrice + range * 0.236);
  const fibtp382 = snap(fibEndPrice + range * 0.382);

  // Create time-bounded retracement lines (start from P1 time)
  const p50 = createFibLevel(fib50, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib50', group, title:'Auto 50%' });
  const p618 = createFibLevel(fib618, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib618', group, title:'Auto 61.8%' });
  const p786 = createFibLevel(fib786, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib786', group, title:'Auto 78.6%' });

  // Create time-bounded take profit lines (start from P1 time)
  const ptp236 = createFibLevel(fibtp236, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp236', group, title:'Auto TP -23.6%' });
  const ptp382 = createFibLevel(fibtp382, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp382', group, title:'Auto TP -38.2%' });

  // Create a single list entry for the Auto-Fib set
  const autoFibSetId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
  const autoFibSetRec = {
    id: autoFibSetId,
    p1: fibStartPrice,
    p2: fibEndPrice,
    kind: 'fibset',
    group,
    handle: null,
    swingType: swing.type
  };
  lines.push(autoFibSetRec);
  addListItem(autoFibSetRec);

  console.log(`Auto-Fib created: ${swing.type} swing from ${fibStartPrice.toFixed(2)} to ${fibEndPrice.toFixed(2)}`);

  // Dispatch custom event to parent (Dash) - Auto-Fib; bubble and target parent.document
  const autoFibCompletedEvent = new CustomEvent('fib_completed', {
    detail: {
      p1: fibStartPrice,
      p2: fibEndPrice,
      fib50: fib50,
      fib618: fib618,
      fib786: fib786,
      fibtp236: fibtp236,
      fibtp382: fibtp382,
      symbol: window.currentSymbol || currentSymbol || 'UNKNOWN',
      exchange: window.currentExchange || currentExchange || 'UNKNOWN',
      timestamp: Date.now(),
      type: 'auto-fib',
      swingType: swing.type
    },
    bubbles: true
  });

  try {
    if (window.parent && window.parent !== window && window.parent.document) {
      window.parent.document.dispatchEvent(autoFibCompletedEvent);
    } else {
      document.dispatchEvent(autoFibCompletedEvent);
    }
  } catch (e) {
    try { window.parent?.dispatchEvent(autoFibCompletedEvent); } catch(_) {}
    try { window.dispatchEvent(autoFibCompletedEvent); } catch(_) {}
  }

  console.log('🎯 Auto-Fib completion event dispatched to Dash');

  // Generate trade details for Auto-Fib
  generateTradeDetails(fibStartPrice, fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp382);
};

// ----- Clear all -----
btnClear.onclick = () => {
  // remove all lines & list
  for (const l of [...lines]) {
    // Handle different types of chart elements
    if (l.handle) {
      // Check if it's a LineSeries-based element (all Fibonacci levels, rectangles, and single lines)
      const isLineSeries = l.kind === 'fibLevel' || l.kind === 'fib1' || l.kind === 'fib2' ||
                          l.kind === 'fib50' || l.kind === 'fib618' || l.kind === 'fib786' ||
                          l.kind === 'fibtp236' || l.kind === 'fibtp382' || l.kind === 'single' ||
                          l.kind === 'rectangle';

      if (isLineSeries) {
        // Remove LineSeries-based elements
        try { chart.removeSeries(l.handle); } catch(e){}
      } else {
        // Remove price line (trendlines don't have handles, they're managed by trendlineSeries removal)
        try { series.removePriceLine(l.handle); } catch(e){}
      }
    }

    // Handle trendline series removal
    if (l.kind === 'trendline' && l.trendlineSeries) {
      try { chart.removeSeries(l.trendlineSeries); } catch(e){}
    }
  }
  lines.length = 0;
  listEl.innerHTML = '';

  // Clear trade details and hide bottom panel
  tradeInfoEl.innerHTML = '<div class="muted">Draw a Fibonacci retracement to generate trade plan</div>';
  const tradePanel = document.getElementById('trade-details-panel');
  if (tradePanel) {
    tradePanel.classList.remove('show');
  }

  post('lw_lines_cleared', {});
};

// ----- Drawings panel toggle -----
btnDrawingsToggle.onclick = () => {
  drawingsPanel.classList.toggle('show');
};

// Close drawings panel when clicking outside
document.addEventListener('click', (e) => {
  if (!drawingsPanel.contains(e.target) && !btnDrawingsToggle.contains(e.target)) {
    drawingsPanel.classList.remove('show');
  }
});

// ----- Expose functions to external sources -----
window.updateChartData = function(newData) {
  if (newData && Array.isArray(newData)) {
    series.setData(newData);
  }
};

// ----- Responsive -----
const ro = new ResizeObserver(entries=>{
  const { width, height } = entries[0].contentRect;
  chart.applyOptions({ width, height });
});
ro.observe(chartEl);


