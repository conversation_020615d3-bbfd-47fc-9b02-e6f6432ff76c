.prepend-text {
    width: 100px;
     /*display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    padding: .375rem .75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    width: 150px;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0rem;
    border-bottom-right-radius: 0rem;
    border-bottom-left-radius: 0.25rem;*/
}

.strat-input {
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
}

.padding-bottom {
    padding-bottom: 20px;
}

.padding-top {
    padding-top: 20px;
}

.div-center {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.strategy-page {
    padding-bottom: 20px;
    padding-top: 20px;
    width: 80%;
    margin: 0 auto;
}

.background-red {
    background-color: Red;
}

.background-yellow {
    background-color: Yellow;
}

.background-green {
    background-color: Green;
}

.background-blue {
    background-color: Blue;
}

.full-width {
    width: 100%;
}

.white-text {
    color: White;
}

.to-top {
    vertical-align: top
}

.other {
    font-size: 12px;
    vertical-align: top
    margin-top: 10px;
    padding-top: 10px;
    display: inline-block;
}

.fs-7 {
    font-size: 14px;
}
.fs-8 {
    font-size: 12px;
}
.fs-9 {
    font-size: 10px;
}

.accordion-buttonXX {
    padding: 0px 10px !important;
    line-height: 1 !important;
    font-size: 14px !important;
}

.accordion-title-customXX {
    width: 100%;
    height: 100%;
    padding: 0.2rem 1rem;
    font-weight: bold;
    background-color: yellow;
    cursor: pointer;
}

/* Trade Notes Hover Effects */
.note-container:hover .note-entry {
    background-color: #f8f9fa !important;
}

.note-container:hover .delete-note-btn {
    opacity: 1 !important;
}

.delete-note-btn:hover {
    background-color: #dc3545 !important;
    color: white !important;
    border-radius: 50% !important;
}

/* Override Bootstrap accordion button background */
.accordion-button {
    background-color: inherit !important;
    padding: 0 !important;
}

.accordion-button:not(.collapsed) {
    background-color: inherit !important;
    box-shadow: none !important;
}

.accordion-button:focus {
    box-shadow: none !important;
    border-color: transparent !important;
}

/* Ensure the title div background shows through */
.accordion-header {
    background-color: transparent !important;
}

/* Hide the default Bootstrap accordion caret */
.accordion-button::after {
    display: none !important;
}

/* Custom caret styling */
.custom-caret {
    transition: transform 0.2s ease;
}

/* Rotate the custom caret when accordion is open */
.accordion-button:not(.collapsed) ~ * .custom-caret,
.accordion-item:has(.accordion-button:not(.collapsed)) .custom-caret {
    transform: rotate(180deg);
}

/* Force the entire accordion item to inherit background */
.accordion-item .accordion-button {
    background: inherit !important;
}

.accordion-item .accordion-header {
    background: inherit !important;
}

/* Target the specific accordion structure */
.accordion-item {
    background: inherit !important;
}