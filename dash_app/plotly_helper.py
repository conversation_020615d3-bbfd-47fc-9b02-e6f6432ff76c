from dash import dcc, Input, Output, State, ctx
import dash_bootstrap_components as dbc
import dash_html_components as html
import dash

app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])

# Example list of exchanges
exchanges = ["bybit", "bitget", "coinbase"]

# Store component to track the active exchange index
app.layout = html.Div([
    dcc.Store(id="active-exchange", data=None),  # Stores currently active exchange index

    dbc.ListGroup(
        [
            dbc.ListGroupItem(
                html.Div([
                    html.Img(
                        src=f"assets/{exchange.lower()}-logo.png",
                        height=20,
                        className="me-2",
                        style={"borderRadius": "50%", "width": "20px", "height": "20px", "objectFit": "cover"}
                    ),
                    html.Span(exchange.capitalize(), className="fw-bold")
                ]),
                id={"type": "exchange-row", "index": i},
                n_clicks=0,
                active=False,
                action=True
            ) for i, exchange in enumerate(exchanges)
        ]
    )
])


# ================================
# Dash Callback
# ================================
@app.callback(
    [Output({"type": "exchange-row", "index": i}, "active") for i in range(len(exchanges))],
    Input({"type": "exchange-row", "index": dash.ALL}, "n_clicks"),
    State("active-exchange", "data"),  # Get the currently active exchange
    prevent_initial_call=True
)
def toggle_active_row(clicks, active_index):
    ctx_id = ctx.triggered_id  # Get which exchange was clicked

    if not ctx_id:
        return [dash.no_update] * len(exchanges)  # No update if no valid click

    clicked_index = ctx_id["index"]  # Extract clicked index

    # Set only the clicked item as active, others inactive
    return [i == clicked_index for i in range(len(exchanges))]


if __name__ == '__main__':
    app.run()
