import dash
import dash_bootstrap_components as dbc
from dash import html, dcc
from flask import session

from dash_app.widgets import search_bar
from dash_app.widgets import manual_trade_entry
from dash_app.widgets import add_order_modal

dash.register_page(__name__, name="TradeCraft - Dashboard")


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")

    return controls_layout


# Define the overall layout.
controls_layout = dbc.Container([
    dcc.Store(id="previous-active-item", data=None),
    dcc.Interval(
        id="price-update-interval",
        interval=30 * 1000,  # Update every 30 seconds
        n_intervals=0
    ),
    html.Div(id="trade-search"),
    dbc.Row(
        [
            dbc.Col("CDOS 1.0", width=6),
            dbc.Col(search_bar.item, width=6),
        ]
    ),
    dbc.Row(id="infoRow", className="pt-3 g-4"),
    dbc.Row(dcc.Graph(id="pnl_graph"), className="padding-top"),
    # Manual Trade Entry Button
    dbc.Row([
        dbc.Col([
            dbc.Button([
                html.I(className="bi bi-plus-circle me-2"),
                "Add Manual Trade"
            ],
            id="open-manual-trade",
            color="primary",
            className="mb-3")
        ], width=12)
    ]),
    html.Div(dbc.Row(
        [
            dbc.Col(html.Div("Status"), width=1),
            dbc.Col(html.Div("Symbol"), width=2),
            dbc.Col(html.Div("Vol."), width=1),
            dbc.Col(html.Div("Time"), width=1),
            dbc.Col(html.Div("Duration"), width=2),
            dbc.Col(html.Div("Direction", className="text-center"), width=1),
            dbc.Col(html.Div("Notional"), width=1),
            dbc.Col(html.Div("Profit"), width=1),
            dbc.Col(html.Div("Fees"), width=1),
            dbc.Col(html.Div(""), width="auto")
        ],
        align="center",
        className="m-0 p-0"
    ), style={"paddingLeft": "5px",
              "paddingRight": "10px",
              "paddingBottom": "10px",
              "paddingTop": "20px"}),
    dbc.Row([
        dbc.Accordion(id='accord', start_collapsed=True, flush=True),
    ], style={"overflow": "auto", "height": "auto"}),

    # Manual Trade Entry Modal
    manual_trade_entry.modal,

    # Add Order Modal
    add_order_modal.modal
], fluid=True,
    style={"paddingBottom": "20px",
           "paddingTop": "20px",
           "width": "90%",
           "overflow": "auto", "height": "auto"})
