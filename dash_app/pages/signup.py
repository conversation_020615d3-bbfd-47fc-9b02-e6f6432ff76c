# dash_app/pages/signup.py

import dash
from dash import html, dcc, Input, Output, State
from flask import session
from flask_bcrypt import Bcrypt
import sqlite3

import trades_db_tables
from trades_db_tables import TradeDB_tables

dash.register_page(__name__, path="/signup", name="TradeCraft - Sign Up")

app = dash.get_app()
bcrypt = Bcrypt(app.server)

layout = html.Div([
    html.H2("Sign Up"),
    dcc.Input(id="signup-username", type="text", placeholder="Username", autoFocus=True),
    dcc.Input(id="signup-password", type="password", placeholder="Password", className="mt-2"),
    html.Button("Create Account", id="signup-button", className="mt-2"),
    html.Div(id="signup-status", className="mt-2"),
], style={"width": "300px", "margin": "50px auto"})


@dash.callback(
    Output("signup-status", "children"),
    Input("signup-button", "n_clicks"),
    State("signup-username", "value"),
    State("signup-password", "value"),
    prevent_initial_call=True
)
def signup_user(n, username, password):
    if not username or not password:
        return "Please enter both a username and password."

    existing_user = TradeDB_tables.login_user(username)
    if existing_user:
        return "Username already taken."

    password_hash = bcrypt.generate_password_hash(password).decode("utf-8")
    try:
        TradeDB_tables.signup_user(username, password_hash)
        return html.Div([
            "✅ Account created! You can now ",
            html.A("log in", href="/login")
        ])
    except Exception as e:
        return f"❌ Error creating account: {str(e)}"
