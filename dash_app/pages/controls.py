import base64
import io
import threading
import traceback
from datetime import datetime, timezone

import dash
import dash_bootstrap_components as dbc
import pandas as pd
from dash import html, dcc, Output, Input, callback, State, ctx, no_update
from flask import session

import helper
import trades_db
from dash_app.widgets.background_tasks import (
    coinbase_status, run_coinbase_import,
    sierra_status, run_sierra_import,
    bybit_status, run_bybit_import,
    blofin_status, run_blofin_import,
    redo_database_status, run_redo_database,
    global_import_running
)
from dash_app.widgets import exchange_account_manager
from models.strategy import Strategy
from models.time_frame import TimeFrame

dash.register_page(__name__, path="/controls", name="TradeCraft - Controls")


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")

    return controls_layout


# Define the overall layout.
controls_layout = html.Div([
    html.Div([
        html.H1("Controls – Import Trade Data", className="mb-4 text-center"),
        # dbc.<PERSON><PERSON>("Add Service", id="open-add-service", color="success", className="position-absolute end-0 mt-2 me-4")
    ], style={"position": "relative"}),

    html.Div([
        dcc.Dropdown(
            id="import-dropdown",
            options=[
                {"label": "Sierra", "value": "sierra"},
                {"label": "Coinbase", "value": "coinbase"},
                {"label": "Bybit", "value": "bybit"},
                {"label": "Blofin", "value": "blofin"},
                {"label": "➕ 𝘼𝘿𝘿 𝙎𝙀𝙍𝙑𝙄𝘾𝙀...", "value": "add_service"}
            ],
            value="coinbase",
            clearable=False,
            style={"width": "300px", "margin": "0 auto 20px auto"}
        )
    ], style={"textAlign": "center"}),

    html.Div(id="tab-content", style={"maxWidth": "600px", "margin": "0 auto"}),

    # Exchange Account Management Section
    html.Div([
        html.Hr(className="my-4"),
        exchange_account_manager.layout
    ], style={"maxWidth": "800px", "margin": "0 auto"}),

    # Data Export/Import Section
    html.Div([
        html.Hr(className="my-4"),
        html.H4("Data Management", className="text-center mb-3"),
        html.Div([
            dbc.Button(["Export Trades Data"],
                       id="export-trades-btn", color="primary", className="me-2"),
            dbc.Button("Import Trades Data", id="import-trades-btn", color="secondary"),
            dbc.Button(["Update All Trades",
                        html.Br(),
                        html.Small(html.I("(Recalculate trade details)"))],
                       id="update-all-trades-btn", color="warning", className="ms-2"),
        ], className="d-flex justify-content-center mb-2"),
        html.Div([
            dbc.Button(["🔄 Redo Database",
                        html.Br(),
                        html.Small(html.I("(Export → Clear → Re-import)"))],
                       id="redo-database-btn", color="danger", className="mt-2"),
        ], className="d-flex justify-content-center"),
        html.Div(id="export-import-status", className="text-center mt-3"),
        html.Div(id="redo-database-status", className="text-center mt-3"),
        dcc.Interval(id="poll-redo-database-status", interval=500, n_intervals=0, disabled=True),

        # Redo Database Confirmation Modal
        dbc.Modal([
            dbc.ModalHeader([
                html.Div([
                    html.I(className="bi bi-exclamation-triangle-fill me-2", style={"fontSize": "1.5rem", "color": "#dc3545"}),
                    html.Span("Confirm Database Reset", style={"fontSize": "1.25rem", "fontWeight": "500"})
                ], className="d-flex align-items-center"),
            ]),
            dbc.ModalBody([
                html.Div([
                    html.P([
                        "⚠️ ", html.Strong("WARNING:"), " This will completely reset your database!"
                    ], className="text-danger mb-3"),
                    html.P("This process will:"),
                    html.Ul([
                        html.Li("Export all your current trade data to CSV"),
                        html.Li("Clear all tables except Users, Timeframes, and Strategies"),
                        html.Li("Re-import the exported data with progress logging")
                    ], className="mb-3"),
                    html.P([
                        html.Strong("This action cannot be undone."),
                        " Make sure you have backups if needed."
                    ], className="text-warning"),
                ])
            ]),
            dbc.ModalFooter([
                dbc.Button("Cancel", id="cancel-redo-database", color="secondary", className="me-2"),
                dbc.Button("Yes, Reset Database", id="confirm-redo-database", color="danger")
            ])
        ], id="redo-database-modal", is_open=False),

        # Hidden file upload component that appears when Import button is clicked
        dcc.Upload(
            id="upload-trades-data",
            children=html.Div([
                "Drag and Drop or ",
                html.A([
                    "Select CSV File",
                ], style={"color": "#007bff", "textDecoration": "underline", "cursor": "pointer"})
            ]),
            style={
                "width": "100%",
                "height": "60px",
                "lineHeight": "60px",
                "borderWidth": "1px",
                "borderStyle": "dashed",
                "borderRadius": "5px",
                "textAlign": "center",
                "margin": "10px 0",
                "display": "none",  # Initially hidden
            },
            multiple=False
        ),
    ], style={"maxWidth": "600px", "margin": "20px auto"}),
])


@callback(
    Output("add-service-modal", "is_open"),
    [Input("open-add-service", "n_clicks"), Input("close-add-service", "n_clicks")],
    [State("add-service-modal", "is_open")],
    prevent_initial_call=True
)
def toggle_modal(open_click, close_click, is_open):
    if ctx.triggered_id in ["open-add-service", "close-add-service"]:
        return not is_open
    return is_open


@callback(
    Output("tab-content", "children"),
    Input("import-dropdown", "value")
)
def render_tab_content(tab):
    if tab == "sierra":
        return dbc.Card([
            dbc.CardBody([
                html.H4("Sierra Chart Trades", className="card-title text-center"),
                dbc.Button("Import Sierra", id="btn-sierra", color="primary", className="d-block mx-auto mb-2"),
                html.Div(id="status-sierra", className="text-center"),
                dcc.Interval(id="poll-sierra-status", interval=500, n_intervals=0, disabled=False)
            ])
        ])

    elif tab == "coinbase":
        return dbc.Card([
            dbc.CardBody([
                html.H4("Coinbase Trades", className="card-title text-center"),
                dbc.Button("Import Coinbase", id="btn-coinbase", color="primary", className="d-block mx-auto mb-2"),
                html.Div(id="status-coinbase", className="text-center"),
                dcc.Interval(id="poll-coinbase-status", interval=500, n_intervals=0, disabled=False)
            ])
        ])

    elif tab == "bybit":
        return dbc.Card([
            dbc.CardBody([
                html.H4("Bybit Trades", className="card-title text-center"),

                # Category dropdown
                html.Div([
                    html.Label("Category:", className="me-2"),
                    dcc.Dropdown(
                        id="bybit-category",
                        options=[
                            {"label": "Linear", "value": "linear"},
                            {"label": "Inverse", "value": "inverse"},
                            {"label": "Spot", "value": "spot"}
                        ],
                        value="linear",
                        clearable=False,
                        style={"width": "150px", "display": "inline-block"}
                    )
                ], className="d-flex align-items-center justify-content-center mb-2"),

                # Settlement coin input
                html.Div([
                    html.Label("Settlement Coin (optional):", className="me-2"),
                    dbc.Input(
                        id="bybit-settlecoin",
                        type="text",
                        value="USDT",
                        placeholder="e.g., USDT",
                        style={"width": "150px", "display": "inline-block"}
                    )
                ], className="d-flex align-items-center justify-content-center mb-3"),

                # Import button
                dbc.Button("Import Bybit", id="btn-bybit", color="primary", className="d-block mx-auto mb-2"),

                # Status display
                dcc.Loading(
                    id="loading-bybit",
                    type="default",
                    children=html.Div(id="status-bybit", className="text-center")
                ),
                dcc.Interval(id="poll-bybit-status", interval=500, n_intervals=0, disabled=False)
            ])
        ])

    elif tab == "blofin":
        return dbc.Card([
            dbc.CardBody([
                html.H4("Blofin Trades", className="card-title text-center"),
                dbc.Button("Import Blofin", id="btn-blofin", color="primary", className="d-block mx-auto mb-2"),
                html.Div(id="status-blofin", className="text-center"),
                dcc.Interval(id="poll-blofin-status", interval=500, n_intervals=0, disabled=False)
            ])
        ])

    elif tab == "add_service":
        return dbc.Card([
            dbc.CardBody([
                html.H4("Connect a New Service", className="card-title text-center mb-4"),
                dcc.Dropdown(
                    id="service-type",
                    options=[
                        {"label": "Blofin", "value": "blofin"},
                        {"label": "Coinbase", "value": "coinbase"},
                        {"label": "Sierra", "value": "sierra"},
                        {"label": "Bybit", "value": "bybit"}
                    ],
                    placeholder="Select service...",
                    className="mb-3"
                ),
                dbc.Input(id="api-key", placeholder="API Key", className="mb-2"),
                dbc.Input(id="api-secret", placeholder="API Secret", className="mb-2"),
                dbc.Input(id="api-name", placeholder="API Key Name", className="mb-2"),
                html.Div([
                    dbc.Button("Save", id="save-service", color="primary", className="me-2"),
                    dbc.Button("Cancel", id="cancel-add-service", color="secondary")
                ], className="text-center mt-3")
            ])
        ])

    return dash.no_update


# ===========================================================================
# Callbacks to start each import.
# ===========================================================================
@callback(
    Output("poll-sierra-status", "disabled"),
    Input("btn-sierra", "n_clicks"),
    prevent_initial_call=True
)
def start_sierra_import(n_clicks):
    if global_import_running["running"]:
        return True
    thread = threading.Thread(target=run_sierra_import)
    thread.start()
    return False


@callback(
    Output("poll-coinbase-status", "disabled"),
    Input("btn-coinbase", "n_clicks"),
    prevent_initial_call=True
)
def start_coinbase_import(n_clicks):
    if global_import_running["running"]:
        return True
    thread = threading.Thread(target=run_coinbase_import, kwargs={
        "username": session.get("username")
    })
    thread.start()
    return False


@callback(
    Output("poll-bybit-status", "disabled"),
    Input("btn-bybit", "n_clicks"),
    State("bybit-category", "value"),
    State("bybit-settlecoin", "value"),
    prevent_initial_call=True
)
def start_bybit_import(n_clicks, category, settleCoin):
    """
    Start the Bybit import process when the Import Bybit button is clicked.

    Args:
        n_clicks: Number of times the button has been clicked
        category: The product category (e.g., "linear", "inverse", "spot")
        settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC")

    Returns:
        bool: Whether the polling interval should be disabled
    """
    if global_import_running["running"]:
        return True

    # Use the values from the UI controls
    # If settleCoin is empty, set it to None
    settleCoin = settleCoin if settleCoin and settleCoin.strip() else None

    thread = threading.Thread(target=run_bybit_import, kwargs={
        "category": category,
        "settleCoin": settleCoin,
        "username": session.get("username")
    })
    thread.start()
    return False


@callback(
    Output("poll-blofin-status", "disabled"),
    Input("btn-blofin", "n_clicks"),
    prevent_initial_call=True
)
def start_blofin_import(n_clicks):
    if global_import_running["running"]:
        return True
    thread = threading.Thread(target=run_blofin_import)
    thread.start()
    return False


# ===========================================================================
# Polling callbacks for each import to update their status display.
# ===========================================================================
@callback(
    Output("status-sierra", "children"),
    Output("poll-sierra-status", "disabled", allow_duplicate=True),
    Input("poll-sierra-status", "n_intervals"),
    prevent_initial_call=True
)
def check_sierra_status(_):
    if sierra_status["running"]:
        return "⏳ Importing Sierra trades...", False
    if sierra_status["error"]:
        return f"Error: {sierra_status['error']}", True
    return ["🚀 No Sierra trades yet – import your first trades to get started!"], True


@callback(
    Output("status-coinbase", "children"),
    Output("poll-coinbase-status", "disabled", allow_duplicate=True),
    Input("poll-coinbase-status", "n_intervals"),
    prevent_initial_call=True
)
def check_coinbase_status(_):
    if coinbase_status["running"]:
        if coinbase_status["status"]:
            return coinbase_status["status"], False
        return "⏳ Importing Coinbase trades...", False
    if coinbase_status["error"]:
        return f"Error: {coinbase_status['error']}", True
    if coinbase_status["result"]:
        return coinbase_status["result"], True

    return get_coinbase_import_status()


def get_coinbase_import_status():
    last_import_date = trades_db.TradesDB.get_coinbase_last_order_time()
    last_import_date = helper.mSToDate(last_import_date)
    if last_import_date:
        duration_ms = helper.calculate_duration_seconds(last_import_date, datetime.now(timezone.utc))
        duration_since_last_import = helper.format_duration(duration_ms)
        return [
            "Last trade imported was ",
            html.Strong(duration_since_last_import),
            " ago ✅"
        ], True

    return ["🚀 No Coinbase trades yet – import your first trades to get started!"], True


@callback(
    Output("status-bybit", "children"),
    Output("poll-bybit-status", "disabled", allow_duplicate=True),
    Input("poll-bybit-status", "n_intervals"),
    State("bybit-category", "value"),
    State("bybit-settlecoin", "value"),
    prevent_initial_call=True
)
def check_bybit_status(_, category, settleCoin):
    """
    Check the status of the Bybit import process.

    Args:
        _: Number of intervals
        category: The product category
        settleCoin: The settlement currency

    Returns:
        tuple: (status message, whether to disable polling)
    """
    if bybit_status["running"]:
        status_msg = f"⏳ Importing Bybit {category} trades"
        if settleCoin and settleCoin.strip():
            status_msg += f" for {settleCoin}"
        status_msg += "..."
        return status_msg, False

    if bybit_status["error"]:
        return f"Error: {bybit_status['error']}", True

    if bybit_status["status"]:
        return bybit_status["status"], True

    return ["🚀 No Bybit trades yet – import your first trades to get started!"], True


@callback(
    Output("status-blofin", "children"),
    Output("poll-blofin-status", "disabled", allow_duplicate=True),
    Input("poll-blofin-status", "n_intervals"),
    prevent_initial_call=True
)
def check_blofin_status(_):
    if blofin_status["running"]:
        if blofin_status["status"]:
            return blofin_status["status"], False
        return "⏳ Importing Blofin trades...", False
    if blofin_status["error"]:
        return f"Error: {blofin_status['error']}", True
    if blofin_status["result"]:
        return blofin_status["result"], True
    return ["🚀 No Blofin trades yet – import your first trades to get started!"], True


# ===========================================================================
# Export Trades Data Callback
# ===========================================================================
@callback(
    Output("export-import-status", "children"),
    Input("export-trades-btn", "n_clicks"),
    prevent_initial_call=True
)
def export_trades_data(n_clicks):
    """Export trades data, strategies, and time frames to a single CSV file."""
    try:
        # Create a list to hold all export data
        export_data = helper.get_export_data()

        if not export_data:
            return html.Div("No data found to export.", className="text-warning")

        # Create a DataFrame for all export data
        export_df = pd.DataFrame(export_data)

        # Generate filename with current date
        current_date = datetime.now().strftime("%Y-%m-%d")
        filename = f"tradecraft_export_{current_date}.csv"

        # Save to CSV
        export_df.to_csv(filename, index=False)

        return html.Div([
            html.I(className="bi bi-check-circle-fill text-success me-2"),
            f"Successfully exported {len(strategies)} strategies, {len(time_frames)} time frames, and {trade_count} trades to {filename}"
        ])

    except Exception as e:
        traceback.print_exc()
        return html.Div([
            html.I(className="bi bi-exclamation-triangle-fill text-danger me-2"),
            f"Error exporting data: {str(e)}"
        ])


# ===========================================================================
# Show Upload Component when the Import Button is Clicked
# ===========================================================================
@callback(
    Output("upload-trades-data", "style"),
    Input("import-trades-btn", "n_clicks"),
    prevent_initial_call=True
)
def show_upload_component(n_clicks):
    """Show the upload component when the Import button is clicked."""
    return {
        "width": "100%",
        "height": "60px",
        "lineHeight": "60px",
        "borderWidth": "1px",
        "borderStyle": "dashed",
        "borderRadius": "5px",
        "textAlign": "center",
        "margin": "10px 0",
        "display": "block"  # Show the component
    }


# ===========================================================================
# Process Uploaded CSV File
# ===========================================================================
@callback(
    Output("export-import-status", "children", allow_duplicate=True),
    Input("upload-trades-data", "contents"),
    State("upload-trades-data", "filename"),
    prevent_initial_call=True
)
def process_uploaded_file(contents, filename):
    """Process the uploaded CSV file and update trades in the database."""
    if contents is None:
        return no_update
    try:
        # Decode the file contents
        content_type, content_string = contents.split(',')
        decoded = base64.b64decode(content_string)

        # Check if it's a CSV file
        if not filename.endswith('.csv'):
            return html.Div([
                html.I(className="bi bi-exclamation-triangle-fill text-danger me-2"),
                "Please upload a CSV file."
            ])

        # Read the CSV file
        df = pd.read_csv(io.StringIO(decoded.decode('utf-8')))

        # Check if this is the new combined format with data_type column
        if "data_type" in df.columns:
            return helper.process_combined_file(df)
        else:
            return html.Div([
                html.I(className="bi bi-exclamation-triangle-fill text-danger me-2"),
                "Unrecognized CSV format. Please upload a valid TradeCraft export file."
            ])

    except Exception as e:
        traceback.print_exc()
        return html.Div([
            html.I(className="bi bi-exclamation-triangle-fill text-danger me-2"),
            f"Error processing file: {str(e)}"
        ])


@callback(
    Output("export-import-status", "children", allow_duplicate=True),
    Input("update-all-trades-btn", "n_clicks"),
    prevent_initial_call=True
)
def update_all_trades(n_clicks):
    """Update all trades by recalculating their details."""
    if not n_clicks:
        return no_update

    try:
        # Get all trades from the database
        trades = trades_db.TradesDB.get_trades()

        if not trades:
            return html.Div([
                html.I(className="bi bi-info-circle-fill text-info me-2"),
                "No trades found to update."
            ])

        # Process each trade
        for trade in trades:
            try:
                trade.update_trade_details()
            except Exception as e:
                print(f"Error updating trade {trade.id_field}: {str(e)}")
                traceback.print_exc()
        updated_count = trades_db.TradesDB.batch_update_trades(trades=trades)

        # Return a success message
        return html.Div([
            html.I(className="bi bi-check-circle-fill text-success me-2"),
            f"Successfully updated {updated_count} of {len(trades)} trades."
        ])

    except Exception as e:
        traceback.print_exc()
        return html.Div([
            html.I(className="bi bi-exclamation-triangle-fill text-danger me-2"),
            f"Error updating trades: {str(e)}"
        ])


# ===========================================================================
# Redo Database Callbacks
# ===========================================================================
@callback(
    Output("redo-database-modal", "is_open"),
    [Input("redo-database-btn", "n_clicks"),
     Input("cancel-redo-database", "n_clicks"),
     Input("confirm-redo-database", "n_clicks")],
    [State("redo-database-modal", "is_open")],
    prevent_initial_call=True
)
def toggle_redo_database_modal(redo_clicks, cancel_clicks, confirm_clicks, is_open):
    """Toggle the redo database confirmation modal."""
    if ctx.triggered_id == "redo-database-btn":
        # Check if another import is running
        if global_import_running["running"]:
            return False  # Don't open modal if import is running
        return True  # Open modal
    elif ctx.triggered_id in ["cancel-redo-database", "confirm-redo-database"]:
        return False  # Close modal
    return is_open


@callback(
    Output("poll-redo-database-status", "disabled"),
    Input("confirm-redo-database", "n_clicks"),
    prevent_initial_call=True
)
def start_redo_database(n_clicks):
    """Start the redo database process when confirmed."""
    if global_import_running["running"]:
        return True
    thread = threading.Thread(target=run_redo_database)
    thread.start()
    return False


@callback(
    Output("redo-database-status", "children"),
    Output("poll-redo-database-status", "disabled", allow_duplicate=True),
    Input("poll-redo-database-status", "n_intervals"),
    prevent_initial_call=True
)
def check_redo_database_status(_):
    """Check the status of the redo database process."""
    if redo_database_status["running"]:
        if redo_database_status["status"]:
            return redo_database_status["status"], False
        return "🔄 Processing database redo...", False
    if redo_database_status["error"]:
        return html.Div([
            html.I(className="bi bi-exclamation-triangle-fill text-danger me-2"),
            f"Error: {redo_database_status['error']}"
        ]), True
    if redo_database_status["result"]:
        return html.Div([
            html.I(className="bi bi-check-circle-fill text-success me-2"),
            redo_database_status["result"]
        ]), True
    return "", True
