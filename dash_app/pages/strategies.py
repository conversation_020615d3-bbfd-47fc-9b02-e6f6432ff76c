"""
Strategies Page Module

This module defines the layout and callbacks for the trading strategies page in the CDOS application.
It allows users to view, create, update, and delete trading strategies. Each strategy contains
information such as name, description, notes, and image links.

The page displays strategies as cards in a grid layout and provides modals for adding and updating
strategies. Users must be logged in to access this page.
"""

import time

import dash
import dash_bootstrap_components as dbc
from dash import html, Output, Input, callback, State, dcc, ALL
from flask import session

import helper
from models.strategy import Strategy
from trades_db import TradesDB

dash.register_page(__name__, name="TradeCraft - Strategies")


def getStratCards():
    """
    Retrieves all strategies from the database and creates a card layout for display.

    This function:
    1. Fetches all strategies from the database
    2. Creates a Bootstrap card for each strategy showing its name, description, and last update time
    3. Organizes the cards into rows with 3 cards per row

    Returns:
        list: A list of dbc.Row components, each containing up to 3 strategy cards
    """
    strategies = TradesDB.getStrategiesList()
    cards = []
    for count, strat in enumerate(strategies):
        card = dbc.Card(
            html.Div(
                [
                    dbc.CardBody(
                        [
                            html.H4(strat.name, className="card-title"),
                            html.P(
                                strat.description,
                                className="card-text",
                            ),
                            html.Small(
                                strat.get_last_update_time(),
                                className="card-text text-muted",
                            )
                        ],
                    ),
                    dbc.CardFooter(html.Small(strat.get_strategy_summary())),
                ], id={"type": "card", "index": strat.strat_id}, n_clicks=0
            ), style={"width": "18rem"}, color="primary", outline=True
        )

        cards.append(card)

    data = [
        dbc.Row(
            [dbc.Col(card, width=4) for card in chunk],
            style={"padding-top": "20px" if count > 0 else ""}
        )
        for count, chunk in enumerate(helper.chunk_list(cards, 3))
    ]

    return data


# UI Components for the strategy page

# Button to add a new strategy
add_strat_button = html.Div([
    dbc.Button("Add new strategy", id="add-strat-button", className="me-2", n_clicks=0)
], style={"padding-left": "20px"})

# Save button for the add strategy modal
save_button = dbc.Col(
    dbc.Button(
        "Save",
        id="save-strategy-button",
        className="ms-auto",
        color="primary",
        type="button",
        n_clicks=0,
    )
)

# Delete button for the update strategy modal
delete_button = dbc.Col(
    dbc.Button(
        "Delete",
        id="delete-strategy-button",
        className="ms-auto, btn-secondary",
        color="danger",
        type="button",
        n_clicks=0
    ),
    width={"offset": 1}
)

# Update button for the update strategy modal
update_button = dbc.Col(
    dbc.Button(
        "Update",
        id="update-strategy-button",
        className="ms-auto",
        color="primary",
        type="button",
        n_clicks=0,
    ),
    width={"offset": 5}
)

# Footer for the add strategy modal
strat_footer = (
    html.Div(
        [
            dbc.Row(save_button)
        ]
    )
)

# Footer for the update strategy modal
update_strat_footer = (
    html.Div(
        [
            dbc.Row(
                [
                    delete_button,
                    update_button
                ]
            )
        ], className="full-width"
    )
)

# Modal for adding a new strategy
add_strat_modal = html.Div(
    [
        dbc.Modal(
            dcc.Loading(
                [
                    dbc.ModalHeader(dbc.ModalTitle("Add new Strategy"), close_button=True),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Name', className='prepend-text')),
                            dbc.Input(placeholder="Strategy Name", id="strat-name-input")
                        ], className="strat-input"),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Description', className='prepend-text')),
                            dbc.Textarea(placeholder="Description", id="strat-desc-input"),
                        ], className="strat-input",
                    ),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Notes', className='prepend-text')),
                            dbc.Textarea(placeholder="Notes", id="strat-notes-input"),
                        ], className="strat-input",
                    ),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Images CSV', className='prepend-text')),
                            dbc.Textarea(placeholder="Images CSV (url,url,url)", id="strat-images-input"),
                        ], className="strat-input padding-bottom",
                    ),
                    dbc.ModalFooter(children=strat_footer),
                ], id="loading-component", overlay_style={"visibility": "visible", "opacity": .5},
            ),
            id="add-strat-modal",
            centered=True,
            is_open=False,
            style={"padding-left": "20px"}
        ),
    ]
)

# Modal for updating an existing strategy
update_strat_modal = html.Div(
    [
        # Store component to keep track of which strategy is being edited
        dcc.Store(id="update-strat-id"),
        dbc.Modal(
            dcc.Loading(
                [
                    dbc.ModalHeader(dbc.ModalTitle("Update Strategy"), close_button=True),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Name', className='prepend-text')),
                            dbc.Input(placeholder="Strategy Name", id="update-strat-name-input")
                        ], className="strat-input"),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Description', className='prepend-text')),
                            dbc.Textarea(placeholder="Description", id="update-strat-desc-input"),
                        ], className="strat-input",
                    ),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Notes', className='prepend-text')),
                            dbc.Textarea(placeholder="Notes", id="update-strat-notes-input"),
                        ], className="strat-input",
                    ),
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText(children=html.Span(children='Images CSV', className='prepend-text')),
                            dbc.Textarea(placeholder="Images CSV (url,url,url)", id="update-strat-images-input"),
                        ], className="strat-input padding-bottom",
                    ),
                    dbc.ModalFooter(children=update_strat_footer),
                ], id="loading-component", overlay_style={"visibility": "visible", "opacity": .5},
            ),
            id="update-strat-modal",
            centered=True,
            is_open=False,
            style={"padding-left": "20px"}
        ),
    ]
)


def layout():
    """
    Defines the layout for the strategies page.

    This function:
    1. Checks if the user is logged in
    2. If not logged in, redirects to the login page
    3. If logged in, returns the strategies page layout

    Returns:
        dash component: Either a redirect to login or the strategies page layout
    """
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")

    return controls_layout


# Define the overall layout.
controls_layout = html.Div(
    [
        # Hidden div to trigger the initial load of strategies
        html.Div(id="strat-page-div", n_clicks=0),
        # Button to add a new strategy
        add_strat_button,
        # Modal for adding a new strategy
        add_strat_modal,
        # Modal for updating an existing strategy
        update_strat_modal,
        # Container for displaying strategy cards
        dbc.Container(id="strat-container", style={"padding-top": "20px"}, fluid=True)
    ], className="strategy-page")


@callback(
    Output("strat-container", "children"),
    Input("strat-page-div", "n_clicks")
)
def show_strategies(n_clicks):
    """
    Callback to load and display strategy cards when the page loads.

    This callback is triggered when the page loads (via the hidden div's n_clicks).
    It fetches all strategies from the database and displays them as cards.

    Args:
        n_clicks: Number of clicks on the hidden div (used to trigger the callback)

    Returns:
        list: A list of dbc.Row components containing strategy cards
    """
    if not n_clicks or all(n == 0 for n in n_clicks):
        return getStratCards()
    return dash.no_update


@callback(
    [
        Output("add-strat-modal", "is_open"),
        Output("strat-name-input", "value"),
        Output("strat-desc-input", "value"),
        Output("strat-notes-input", "value"),
        Output("strat-images-input", "value")
    ],
    [Input("add-strat-button", "n_clicks")],
    [State("add-strat-modal", "is_open")], config_prevent_initial_callbacks=True
)
def toggle_strat_modal(n1, is_open):
    """
    Callback to toggle the add strategy modal.

    This callback is triggered when the "Add new strategy" button is clicked.
    It opens the modal if it's closed, or closes it if it's open.
    When opening the modal, it clears all input fields.

    Args:
        n1: Number of clicks on the "Add new strategy" button
        is_open: Current state of the modal (open or closed)

    Returns:
        tuple: (
            is_open: New state of the modal,
            name_value: Value for the name input field,
            desc_value: Value for the description input field,
            notes_value: Value for the notes input field,
            images_value: Value for the images input field
        )
    """
    if n1:
        if is_open:
            return not is_open, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        else:
            return not is_open, "", "", "", ""

    return is_open, dash.no_update, dash.no_update, dash.no_update, dash.no_update


@callback(
    Output("strat-container", "children", allow_duplicate=True),
    Input("save-strategy-button", "n_clicks"),
    State("strat-name-input", "value"),
    State("strat-desc-input", "value"),
    State("strat-notes-input", "value"),
    State("strat-images-input", "value"),
    prevent_initial_call=True,
    running=[(Output("loading-component", "display"), "show", "hide"),
             (Output("add-strat-modal", "is_open"), True, False)]
)
def save_strategy(n1, name, description, notes, images):
    """
    Callback to save a new strategy to the database.

    This callback is triggered when the "Save" button in the add strategy modal is clicked.
    It creates a new Strategy object with the provided information and saves it to the database.
    After saving, it refreshes the strategy cards display.

    Args:
        n1: Number of clicks on the "Save" button
        name: Name of the strategy
        description: Description of the strategy
        notes: Notes for the strategy
        images: CSV string of image URLs

    Returns:
        list: Updated list of strategy cards
    """
    if n1 is None or n1 == 0 or name is None or name == "":
        return dash.no_update
    now_date = helper.get_now_date()
    strat = Strategy.newStrategy(name=name, description=description, notes=notes, images=images,
                                 createdDate=now_date, modifiedDate=now_date)
    TradesDB.save_new_strategy(strat)
    return getStratCards()


@callback(
    [
        Output("update-strat-id", "data"),
        Output("update-strat-modal", "is_open", allow_duplicate=True),
        Output("update-strat-name-input", "value", allow_duplicate=True),
        Output("update-strat-desc-input", "value", allow_duplicate=True),
        Output("update-strat-notes-input", "value", allow_duplicate=True),
        Output("update-strat-images-input", "value", allow_duplicate=True),
        Input({"type": "card", "index": ALL}, "n_clicks"),
    ], config_prevent_initial_callbacks=True
)
def select_strategy(n_clicks):
    """
    Callback to select a strategy for editing.

    This callback is triggered when a strategy card is clicked.
    It opens the update modal and populates it with the selected strategy's information.

    Args:
        n_clicks: List of click counts for all strategy cards

    Returns:
        tuple: (
            strat_id: ID of the selected strategy,
            is_open: True to open the modal,
            name: Name of the selected strategy,
            description: Description of the selected strategy,
            notes: Notes of the selected strategy,
            images: Image URLs of the selected strategy
        )
    """
    if not n_clicks or all(n == 0 for n in n_clicks):
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update

    triggered_id = dash.ctx.triggered_id
    strat = TradesDB.getStrategyById(stratId=triggered_id["index"])

    return triggered_id["index"], True, strat.name, strat.description, strat.notes, strat.images


@callback(
    Output("strat-container", "children", allow_duplicate=True),
    Input("update-strategy-button", "n_clicks"),
    State("update-strat-name-input", "value"),
    State("update-strat-desc-input", "value"),
    State("update-strat-notes-input", "value"),
    State("update-strat-images-input", "value"),
    State("update-strat-id", "data"),
    prevent_initial_call=True,
    running=[(Output("loading-component", "display"), "show", "hide"),
             (Output("update-strat-modal", "is_open"), True, False)]
)
def update_strategy(n1, name, description, notes, images, strat_id):
    """
    Callback to update an existing strategy.

    This callback is triggered when the "Update" button in the update strategy modal is clicked.
    It updates the selected strategy with the new information provided in the form.
    After updating, it refreshes the strategy cards display.

    Args:
        n1: Number of clicks on the "Update" button
        name: Updated name of the strategy
        description: Updated description of the strategy
        notes: Updated notes for the strategy
        images: Updated CSV string of image URLs
        strat_id: ID of the strategy to update

    Returns:
        list: Updated list of strategy cards
    """
    if n1 is None or n1 == 0 or name is None or name == "":
        return dash.no_update

    strat = TradesDB.getStrategyById(stratId=strat_id)
    strat.name = name
    strat.description = description
    strat.notes = notes
    strat.images = images
    TradesDB.updateStrategy(strat)
    return getStratCards()


@callback(
    Output("strat-container", "children", allow_duplicate=True),
    Input("delete-strategy-button", "n_clicks"),
    State("update-strat-id", "data"),
    prevent_initial_call=True,
    running=[(Output("loading-component", "display"), "show", "hide"),
             (Output("update-strat-modal", "is_open"), True, False)]
)
def delete_strategy(n1, strat_id):
    """
    Callback to delete a strategy.

    This callback is triggered when the "Delete" button in the update strategy modal is clicked.
    It deletes the selected strategy from the database.
    After deletion, it refreshes the strategy cards display.

    Args:
        n1: Number of clicks on the "Delete" button
        strat_id: ID of the strategy to delete

    Returns:
        list: Updated list of strategy cards (with the deleted strategy removed)
    """
    if n1 is None or n1 == 0:
        return dash.no_update

    TradesDB.deleteStrategyById(stratId=strat_id)
    return getStratCards()
