import dash
import dash_bootstrap_components as dbc
from dash import html, dcc, dash_table
from flask import session
import plotly.graph_objects as go
import plotly.express as px
from dash_app.widgets import search_bar

dash.register_page(__name__, name="TradeCraft - Analytics")


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")

    return controls_layout


def create_analytics_cards():
    """Create the main analytics cards section"""
    return dbc.Row([
        # Statistics Cards
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H6("Total Trades", className="card-subtitle mb-2 text-muted"),
                    html.H4("1,234", className="card-title text-primary"),
                    html.Small("↑ 12.5% from last month", className="text-success")
                ])
            ], className="mb-3")
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H6("Win Rate", className="card-subtitle mb-2 text-muted"),
                    html.H4("67.8%", className="card-title text-success"),
                    html.Small("↑ 2.3% from last month", className="text-success")
                ])
            ], className="mb-3")
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H6("Total P&L", className="card-subtitle mb-2 text-muted"),
                    html.H4("$45,678", className="card-title text-success"),
                    html.Small("↑ $5,234 from last month", className="text-success")
                ])
            ], className="mb-3")
        ], width=3),

        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H6("Avg Trade Size", className="card-subtitle mb-2 text-muted"),
                    html.H4("$2,345", className="card-title"),
                    html.Small("↓ 1.2% from last month", className="text-danger")
                ])
            ], className="mb-3")
        ], width=3),
    ], className="mb-4")


def create_trade_analysis_section():
    """Create the Long/Short trade analysis section"""
    return dbc.Row([
        # Long Trades Section
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Long", className="mb-0 text-success")
                ]),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            html.H3("342", className="text-success"),
                            html.P("Long Trade Win-Rate", className="text-muted small")
                        ], width=6),
                        dbc.Col([
                            html.H4("1.23%", className="text-success"),
                            html.P("Avg Long Trade Duration", className="text-muted small")
                        ], width=6)
                    ]),
                    html.Hr(),
                    html.P("Time Long Gain and P&L", className="text-muted small"),
                    html.H5("No Trades", className="text-muted")
                ])
            ], className="mb-3")
        ], width=6),
        
        # Short Trades Section  
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Shorts", className="mb-0 text-danger")
                ]),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            html.H3("20", className="text-danger"),
                            html.P("Short Trade Win-Rate", className="text-muted small")
                        ], width=6),
                        dbc.Col([
                            html.H4("1.23%", className="text-danger"),
                            html.P("Avg Short Trade Duration", className="text-muted small")
                        ], width=6)
                    ]),
                    html.Hr(),
                    html.P("Time Short Gain and P&L", className="text-muted small"),
                    html.H5("No Trades", className="text-muted")
                ])
            ], className="mb-3")
        ], width=6)
    ], className="mb-4")


def create_tables_section():
    """Create the tables section for winning/losing trades"""
    winning_trades_data = [
        {"Ticker": "AAPL", "No. of Trades": 15, "Avg Duration": "2d 3h 45m", "Avg Size": "$2,500.00", "Avg Gain": "3.2%"},
        {"Ticker": "GOOGL", "No. of Trades": 8, "Avg Duration": "1d 12h 30m", "Avg Size": "$3,200.00", "Avg Gain": "2.8%"},
        {"Ticker": "MSFT", "No. of Trades": 12, "Avg Duration": "3d 8h 15m", "Avg Size": "$1,800.00", "Avg Gain": "4.1%"},
        {"Ticker": "TSLA", "No. of Trades": 6, "Avg Duration": "1d 6h 20m", "Avg Size": "$4,100.00", "Avg Gain": "5.7%"},
    ]
    
    losing_trades_data = [
        {"Ticker": "NVDA", "No. of Trades": 3, "Avg Duration": "4h 30m", "Avg Size": "$2,800.00", "Avg Loss": "-2.1%"},
        {"Ticker": "AMD", "No. of Trades": 5, "Avg Duration": "1d 2h 15m", "Avg Size": "$1,900.00", "Avg Loss": "-1.8%"},
        {"Ticker": "META", "No. of Trades": 2, "Avg Duration": "6h 45m", "Avg Size": "$3,500.00", "Avg Loss": "-3.2%"},
    ]
    
    return dbc.Row([
        # Winning Trades Table
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Winning Trades", className="mb-0 text-success")
                ]),
                dbc.CardBody([
                    dash_table.DataTable(
                        data=winning_trades_data,
                        columns=[
                            {"name": "Ticker", "id": "Ticker"},
                            {"name": "No. of Trades", "id": "No. of Trades"},
                            {"name": "Avg Duration", "id": "Avg Duration"},
                            {"name": "Avg Size", "id": "Avg Size"},
                            {"name": "Avg Gain", "id": "Avg Gain"}
                        ],
                        style_cell={'textAlign': 'left', 'padding': '10px'},
                        style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'},
                        style_data_conditional=[
                            {
                                'if': {'column_id': 'Avg Gain'},
                                'color': 'green',
                                'fontWeight': 'bold'
                            }
                        ]
                    )
                ])
            ], className="mb-3")
        ], width=6),
        
        # Losing Trades Table
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Losing Trades", className="mb-0 text-danger")
                ]),
                dbc.CardBody([
                    dash_table.DataTable(
                        data=losing_trades_data,
                        columns=[
                            {"name": "Ticker", "id": "Ticker"},
                            {"name": "No. of Trades", "id": "No. of Trades"},
                            {"name": "Avg Duration", "id": "Avg Duration"},
                            {"name": "Avg Size", "id": "Avg Size"},
                            {"name": "Avg Loss", "id": "Avg Loss"}
                        ],
                        style_cell={'textAlign': 'left', 'padding': '10px'},
                        style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'},
                        style_data_conditional=[
                            {
                                'if': {'column_id': 'Avg Loss'},
                                'color': 'red',
                                'fontWeight': 'bold'
                            }
                        ]
                    )
                ])
            ], className="mb-3")
        ], width=6)
    ], className="mb-4")


def create_charts_section():
    """Create charts section with sample data"""
    # Sample chart data
    fig1 = go.Figure(data=[
        go.Bar(name='Trades by Day', x=['Mon', 'Tue', 'Wed', 'Thu', 'Fri'], y=[20, 14, 23, 25, 22])
    ])
    fig1.update_layout(title="Trades by Day", height=300)
    
    fig2 = go.Figure(data=[
        go.Bar(name='Trades by Hour', x=['9AM', '10AM', '11AM', '12PM', '1PM', '2PM', '3PM'], y=[5, 8, 12, 15, 18, 10, 7])
    ])
    fig2.update_layout(title="Trades by Hour", height=300)
    
    return dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    dcc.Graph(figure=fig1)
                ])
            ])
        ], width=6),
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    dcc.Graph(figure=fig2)
                ])
            ])
        ], width=6)
    ], className="mb-4")


# Define the overall layout with search bar
controls_layout = dbc.Container([
    # Add the search bar at the top
    html.Div(id="trade-search"),
    dbc.Row([
        dbc.Col("Analytics Dashboard", width=6),
        dbc.Col(search_bar.item, width=6),
    ], className="mb-4"),
    
    # Analytics cards
    create_analytics_cards(),
    
    # Long/Short analysis
    create_trade_analysis_section(),
    
    # Tables section
    create_tables_section(),
    
    # Charts section
    create_charts_section(),
    
], fluid=True, style={"padding": "20px"})
