# dash_app/pages/login.py

import dash
from dash import html, dcc, Input, Output, State
from flask import session
from flask_bcrypt import Bcrypt

from trades_db_tables import TradeDB_tables

dash.register_page(__name__, path="/login", name="TradeCraft - Login")

app = dash.get_app()
bcrypt = Bcrypt(app.server)

layout = html.Div([
    html.H2("Login"),
    dcc.Input(id="login-username", type="text", placeholder="Username", autoFocus=True),
    dcc.Input(id="login-password", type="password", placeholder="Password", className="mt-2"),
    html.Button("Login", id="login-button", className="mt-2"),
    html.Div(id="login-status", className="mt-2"),
    html.Div([
        html.A("Don't have an account? Sign up", href="/signup", className="mt-3")
    ], id="signup-link-container", className="mt-3")
], style={"width": "300px", "margin": "50px auto"})


@dash.callback(
    [Output("login-status", "children"),
     Output("login-logout-button", "children", allow_duplicate=True),
     Output("login-logout-button", "color", allow_duplicate=True),
     Output("login-username", "disabled", allow_duplicate=True),
     Output("login-password", "disabled", allow_duplicate=True),
     Output("login-button", "disabled", allow_duplicate=True),
     Output("signup-link-container", "style", allow_duplicate=True)],
    Input("login-button", "n_clicks"),
    State("login-username", "value"),
    State("login-password", "value"),
    prevent_initial_call=True
)
def login_user(n, username, password):
    if session.get("user_id"):
        return (html.Div([
            "✅ Login successful. Go to ",
            html.A("Dashboard", href="/")
        ]),
                "Logout",
                "danger",
                True,
                True,
                True,
                {"display": "none"})
    elif n is None:
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
    row = TradeDB_tables.login_user(username)

    if row and bcrypt.check_password_hash(row[1], password):
        session["user_id"] = row[0]
        session["username"] = username
        return (html.Div([
            "✅ Login successful. Go to ",
            html.A("Dashboard", href="/")
        ]),
                "Logout",
                "danger",
                True,
                True,
                True,
                {"display": "none"})
    else:
        return (html.Div("❌ Invalid username or password"),
                dash.no_update,
                dash.no_update,
                dash.no_update,
                dash.no_update,
                dash.no_update,
                dash.no_update)
