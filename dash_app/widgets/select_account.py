from dash import dash
import dash_bootstrap_components as dbc
from dash import html, dcc
from dash.dependencies import Input, Output, State, ALL


# ================================
# Helper Functions
# ================================

def get_exchange_list():
    return ["bybit", "bitget", "coinbase"]


def build_exchange_list():
    count = 0
    # Start with the "All" item (always visible)
    exchange_list = [
        dbc.ListGroupItem(
            html.Div([
                html.Div([
                    html.I(className="bi bi-arrow-left-right me-2"),  # Icon
                    html.Span("All", className="fw-bold"),  # Keeps "All" next to the icon
                ], className="d-flex align-items-center"),
                html.Span("3 total accounts", className="text-muted", style={"fontSize": "12px"}),
                dcc.Store(id="active-exchange", data=None),  # Stores currently active exchange index
            ]),
            active=True, action=True, id={"type": "exchange-row", "index": "all"}
        )
    ]

    # Dynamically add items for each exchange
    for exchange in get_exchange_list():
        count += 1
        # Convert exchange name to lowercase for matching asset filenames (adjust if needed)
        logo_filename = f"/assets/{exchange.lower()}-logo.png"
        exchange_list.append(
            dbc.ListGroupItem(
                html.Div([
                    html.Img(
                        src=logo_filename,
                        height=20,
                        className="me-2",
                        style={"borderRadius": "50%", "width": "20px", "height": "20px", "objectFit": "cover"}
                    ),

                    html.Span(exchange.capitalize(), className="fw-bold"),  # Exchange name
                    html.Span("1 account available", className="text-muted d-block", style={"fontSize": "12px"})
                ]),
                action=True, id={"type": "exchange-row", "index": exchange}
            )
        )

    return exchange_list


def build_account_checklist():
    exchanges = ["bybit", "bitget", "coinbase"]  # TradesDB.get_exchange_names()
    return [{"label": f" {exchange.capitalize()}", "value": exchange.lower()} for exchange in exchanges]


# ================================
# Dash Layout
# ================================

modal = dbc.Modal([
    dbc.ModalHeader("Select Account"),
    dbc.ModalBody(
        dbc.Row([
            # Sidebar for Accounts List
            dbc.Col([
                dbc.ListGroup(children=build_exchange_list(), id="exchange_list", flush=True)
            ], width=4, className="border-end"),

            # Right Section: Sub-Accounts Selection
            dbc.Col([
                dbc.Checklist(
                    options=build_account_checklist(),
                    value=[],  # Pre-selected
                    id="account-checklist",
                    inline=False,
                    switch=True,
                    className="mb-3"
                ),
                html.Hr(),
                dbc.Checklist(
                    options=[{"label": " Select all", "value": "select_all"}],
                    value=[],
                    id="select-all-accounts",
                    switch=True
                ),
            ], width=8),
        ])
    ),
    dbc.ModalFooter([
        dbc.Button("Cancel", id="cancel-account", className="me-2", color="secondary"),
        dbc.Button("Confirm", id="confirm-account", color="primary")
    ])
], id="modal-account", is_open=False, size="lg")

# ================================
# Dash Callbacks
# ================================

def get_callbacks(app):
    @app.callback(
        Output("account-checklist", "value"),  # Update account checklist selection
        Input("select-all-accounts", "value"),  # Select all checkbox
        State("account-checklist", "options")  # Get all available accounts
    )
    def toggle_select_all(select_all_value, account_options):
        # Extract values from options
        all_values = [opt["value"] for opt in account_options]

        # If "select_all" is checked, return all values; otherwise, return an empty list
        return all_values if "select_all" in select_all_value else []

    @app.callback(
        [Output({"type": "exchange-row", "index": i}, "active") for i in ["all"] + get_exchange_list()],
        Input({"type": "exchange-row", "index": ALL}, "n_clicks"),
        State("active-exchange", "data"),  # Get the currently active exchange
        prevent_initial_call=True
    )
    def toggle_active_row(clicks, active_index):
        ctx_id = dash.ctx.triggered_id  # Get which exchange was clicked

        if not ctx_id:
            return [dash.no_update] * len(["all"] + get_exchange_list())  # No update if no valid click

        clicked_index = ctx_id["index"]  # Extract clicked index

        # Set only the clicked item as active, others inactive
        return [i == clicked_index for i in ["all"] + get_exchange_list()]

    @app.callback(
        [Output("account-checklist", "value", allow_duplicate=True),
         Output("account-checklist", "options", allow_duplicate=True),
         Output("select-all-accounts", "style", allow_duplicate=True),  # Hides "Select All" if needed
         Output("select-all-accounts", "value", allow_duplicate=True)],  # Toggles "Select All" on if needed
        [Input({'type': 'exchange-row', 'index': ALL}, "n_clicks")],  # List of clickable exchanges
        State({'type': 'exchange-row', 'index': ALL}, "children"),
        prevent_initial_call=True
    )
    def update_account_selection(clicks, list_items):
        """
        Updates the account checklist based on which ListGroupItem is clicked.

        - If "All" is clicked → Selects all accounts and shows "Select All".
        - If a specific exchange is clicked → Selects only that exchange and hides "Select All".
        """

        # Get the triggered input ID
        triggered = dash.ctx.triggered_id

        if not triggered:
            return dash.no_update

        # clicked_list_item = triggered["type"]
        clicked_list_index = triggered["index"]

        # Get all account values
        all_accounts = [opt["value"] for opt in build_account_checklist()]

        # Handle "All" case
        if clicked_list_index == "all":
            return (all_accounts,
                    build_account_checklist(),  # Show all exchange toggles
                    {"display": "block"},  # Show "Select All"
                    ['select_all'])  # Toggle "Select All"

        # If a specific exchange is clicked, return only that exchange
        return ([clicked_list_index],
                [{"label": clicked_list_index.capitalize(), "value": clicked_list_index}],
                # Show toggles for just this exchange
                {"display": "none"},  # Hide "Select All"
                dash.no_update)  # Dont update "Select All" toggle
