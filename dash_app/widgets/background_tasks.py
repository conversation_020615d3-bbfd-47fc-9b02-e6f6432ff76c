# background_tasks.py
import traceback

import cdos  # your cdos module with process functions
from models.trade import Exchange

# Global flag to ensure only one import runs at a time.
global_import_running = {"running": False, "importer": None}

# Per-import status dictionaries
coinbase_status_by_user = {}
coinbase_status = {"running": False, "error": None, "result": None, "status": ""}
sierra_status = {"running": False, "error": None, "result": None, "status": ""}
bybit_status = {"running": False, "error": None, "result": None, "status": ""}
blofin_status = {"running": False, "error": None, "result": None, "status": ""}
redo_database_status = {"running": False, "error": None, "result": None, "status": ""}


def run_coinbase_import(username: str = None):
    # Mark globally that an import is running (if not already running)
    if global_import_running["running"]:
        return  # another import is in progress; do nothing
    global_import_running.update({"running": True, "importer": "coinbase"})
    coinbase_status["running"] = True
    coinbase_status["error"] = None
    try:
        cdos.get_new_orders(status=coinbase_status,
                            exchange=Exchange.COINBASE,
                            username=username)
    except Exception as e:
        traceback.print_exc()
        error_msg = f"❌ Coinbase import failed: {str(e)}"
        print(error_msg, flush=True)  # ensures immediate output
        coinbase_status["error"] = str(e)
    finally:
        coinbase_status["running"] = False
        global_import_running.update({"running": False, "importer": None})


def run_sierra_import():
    if global_import_running["running"]:
        return
    global_import_running.update({"running": True, "importer": "sierra"})
    sierra_status["running"] = True
    sierra_status["error"] = None
    try:
        cdos.process_sierra_orders()
    except Exception as e:
        error_msg = f"❌ Sierra import failed: {str(e)}"
        print(error_msg, flush=True)  # ensures immediate output
        sierra_status["error"] = str(e)
    finally:
        sierra_status["running"] = False
        global_import_running.update({"running": False, "importer": None})


def run_bybit_import(category: str = "linear", settleCoin: str = None, username: str = None):
    """
    Run the Bybit import process.

    Args:
        category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
        settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.
        :param category:
        :param settleCoin:
        :param username:
    """
    if global_import_running["running"]:
        return
    global_import_running.update({"running": True, "importer": "bybit"})
    bybit_status["running"] = True
    bybit_status["error"] = None
    try:
        cdos.get_new_orders(
            username=username,
            status=bybit_status,
            exchange=Exchange.BYBIT,
            category=category,
            settleCoin=settleCoin
        )
    except Exception as e:
        traceback.print_exc()  # prints full stack trace to stdout
        bybit_status["error"] = str(e)
    finally:
        bybit_status["running"] = False
        global_import_running.update({"running": False, "importer": None})


def run_blofin_import():
    if global_import_running["running"]:
        return
    global_import_running.update({"running": True, "importer": "blofin"})
    blofin_status["running"] = True
    blofin_status["error"] = None
    try:
        blofin_status["result"] = cdos.process_blofin_orders(blofin_status)
    except Exception as e:
        error_msg = f"❌ Blofin import failed: {str(e)}"
        print(error_msg, flush=True)  # ensures immediate output
        blofin_status["error"] = str(e)
    finally:
        blofin_status["running"] = False
        blofin_status["status"] = None
        global_import_running.update({"running": False, "importer": None})


def run_redo_database():
    """Run the complete redo database process in the background"""
    import pandas as pd
    import helper
    import trades_db
    from datetime import datetime

    # Mark globally that a process is running
    if global_import_running["running"]:
        return  # another process is in progress; do nothing
    global_import_running.update({"running": True, "importer": "redo_database"})
    redo_database_status["running"] = True
    redo_database_status["error"] = None
    redo_database_status["result"] = None

    try:
        print("🔄 Starting database redo process...")
        redo_database_status["status"] = "🔄 Starting database redo process..."

        # Step 1: Export trades data
        print("📤 Step 1: Exporting trades data...")
        redo_database_status["status"] = "📤 Step 1/3: Exporting trades data..."

        # Get export data using the existing helper function
        export_data = helper.get_export_data()
        print(f"   ✅ Generated export data with {len(export_data)} total records")

        if not export_data:
            redo_database_status["error"] = "No data found to export. Database redo cancelled."
            return

        # Create a DataFrame for all export data
        export_df = pd.DataFrame(export_data)

        # Generate filename with current date
        current_date = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"tradecraft_redo_backup_{current_date}.csv"

        # Save to CSV
        export_df.to_csv(filename, index=False)
        print(f"   ✅ Exported data to {filename}")

        # Step 2: Clear database tables
        print("🗑️ Step 2: Clearing database tables...")
        redo_database_status["status"] = f"🗑️ Step 2/3: Clearing database tables... (Backup saved: {filename})"

        cleared_count = trades_db.TradesDB.clear_trade_data()
        print(f"   ✅ Cleared {cleared_count} tables")

        # Step 3: Re-import the data
        print("📥 Step 3: Re-importing data...")
        redo_database_status["status"] = "📥 Step 3/3: Re-importing data..."

        # Process the exported data directly without importing controls.py
        import_result = helper.process_combined_file(export_df)
        print(f"   ✅ Re-imported data: {import_result}")

        print("✅ Database redo completed successfully!")

        # Count the different data types for the result message
        strategies_count = len([item for item in export_data if item.get("data_type") == "STRATEGY"])
        time_frames_count = len([item for item in export_data if item.get("data_type") == "TIME_FRAME"])
        trades_count = len([item for item in export_data if item.get("data_type") == "TRADE"])

        redo_database_status[
            "result"] = f"✅ Database redo completed! Exported {strategies_count} strategies, {time_frames_count} time frames, and {trades_count} trades. Cleared {cleared_count} tables and re-imported all data. Backup saved as: {filename}"

    except Exception as e:
        error_msg = f"❌ Database redo failed: {str(e)}"
        print(error_msg, flush=True)
        traceback.print_exc()
        redo_database_status["error"] = str(e)
    finally:
        redo_database_status["running"] = False
        redo_database_status["status"] = None
        global_import_running.update({"running": False, "importer": None})
