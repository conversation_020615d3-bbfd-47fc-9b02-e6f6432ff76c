from datetime import datetime

import dash_bootstrap_components as dbc
import dash
from dash import html, dcc
from dash.dependencies import Input, Output, State
import math

from trades_db import TradesDB


# Sample list of cryptos (1000 for testing)
# crypto_list = [f"Crypto_{i}" for i in range(1, 500)]


# ================================
# Helper Functions
# ================================

def build_crypto_list(symbol_list, select_all_value, filtered_list):
    list_to_use = filtered_list if filtered_list is not None else symbol_list

    # Determine number of columns needed (10 per column)
    num_columns = math.ceil(len(list_to_use) / 10)

    # Create table columns (each with max 10 items)
    table_columns = []
    for i in range(num_columns):
        column_cryptos = list_to_use[i * 10:(i + 1) * 10]  # Slice into groups of 10
        checklist = dbc.Checklist(
            options=[{"label": f" {crypto}", "value": crypto} for crypto in column_cryptos],
            value=column_cryptos if "select_all" in select_all_value else [],
            id={"type": "crypto-checklist", "index": i}, switch=True,
            style={"backgroundColor": "transparent"}
        )
        table_columns.append(html.Td(checklist, style={"verticalAlign": "top"}))  # Table cell for each column

    # Create the table with scrollable columns
    table = dbc.Table(
        html.Tr(table_columns),  # Place columns in a single horizontal row
        bordered=False, striped=False, hover=False, responsive=True
    )

    return table


# ================================
# Dash Layout
# ================================

modal = dbc.Modal([
    dbc.ModalHeader("Select Symbol"),
    dbc.ModalBody([
        # Search Bar
        dcc.Input(
            id="crypto-search", type="text", placeholder="Search...",
            debounce=False, style={"width": "100%", "marginBottom": "10px", "padding": "8px", "borderRadius": "5px"}
        ),
        dcc.Store(id="update-crypto-table", data=False),
        dcc.Store(id="update-select-all-state", data=False),
        dcc.Store(id="search-state", data=""),
        dcc.Store(id="symbol-list"),
        # Crypto Table with Scroll
        html.Div(
            id="crypto-list-container",
            style={"overflowX": "auto",
                   "whiteSpace": "nowrap",
                   "maxWidth": "100%",
                   # "scrollbar-width": "thin",
                   # "scrollbar-height": "thin"
                   }
        ),

        # Select All and Buttons
        dbc.Row([
            dbc.Col(
                dbc.Checklist(
                    options=[{"label": " Select all", "value": "select_all"}],
                    value=["select_all"], id="select-all-checkbox", switch=True
                ), width=6
            ),
            # dbc.Col([
            #     dbc.Button("Cancel", id="cancel-button", color="secondary", className="me-2"),
            #     dbc.Button("Confirm", id="confirm-button", color="primary")
            # ], width=6, className="d-flex justify-content-end")
        ], className="mt-2")
    ], className="shadow-sm",
        style={"borderRadius": "10px", "padding": "10px", "width": "100%"}),
    dbc.ModalFooter(dbc.Button("Close", id="close-symbol", className="ms-auto", n_clicks=0))
], id="modal-symbol", is_open=False)


# ================================
# Dash Callbacks
# ================================

def get_callbacks(app):
    @app.callback(
        Output("crypto-list-container", "children"),
        Output("symbol-list", "data"),
        Input("modal-symbol", "is_open"),
        prevent_initial_call=True
    )
    def add_symbols(is_open):
        # Only update list if search box is being "opened"
        if not is_open:
            return dash.no_update, dash.no_update

        # Find trades between dates and get symbols list
        symbols_list = TradesDB.get_symbols_list()
        return build_crypto_list(symbols_list, ['select_all'], None), symbols_list

    @app.callback(
        [Output("crypto-list-container", "children", allow_duplicate=True),
         Output("update-crypto-table", "data"),
         Output("update-select-all-state", "data"),
         Output("search-state", "data")],
        [Input("crypto-search", "value"),
         Input("select-all-checkbox", "value")],
        State("update-select-all-state", "data"),
        State("search-state", "data"),
        State("symbol-list", "data"),
        prevent_initial_call=True,
    )
    def search_symbols(search_text, select_all_value, update_select_all_st, search_state, symbol_list):
        if search_text is None:
            search_text = ""
        search_change = search_text is not search_state

        ctx = dash.callback_context
        if update_select_all_st and not search_change or not ctx.triggered or ctx.triggered[0]["value"] == 0:
            return [dash.no_update, True, False, search_text]

        # Filter list based on search
        filtered_list = [c for c in symbol_list if search_text.lower() in c.lower()] if search_text else symbol_list

        table = build_crypto_list(symbol_list, select_all_value, filtered_list)

        return [table, False if search_change else True, False, search_text]

    def all_sublists_in_list(list_of_lists, main_list):
        # Flatten the sublists into a single set
        flattened_set = set(item for sublist in list_of_lists for item in sublist)

        # Check if all items in main_list are in the flattened set
        return set(main_list).issubset(flattened_set)

    # Callback to update the Select All checkbox state
    @app.callback(
        [Output("select-all-checkbox", "value"),
         Output("update-select-all-state", "data", allow_duplicate=True)],
        Input({"type": "crypto-checklist", "index": dash.ALL}, "value"),
        Input("update-crypto-table", "data"),
        Input("select-all-checkbox", "value"),
        State("search-state", "data"),
        State("symbol-list", "data"),
        prevent_initial_call=True
    )
    def select_all(selected_cryptos_list, update_crypto_tab, select_all_checkbox, search_text,
                   symbol_list):
        ctx = dash.callback_context
        if not ctx.triggered or ctx.triggered[0]["value"] == 0 or len(ctx.triggered) == 5:  # Ignore if auto-triggered
            return dash.no_update, dash.no_update

        filtered_list = [c for c in symbol_list if search_text.lower() in c.lower()] if search_text else symbol_list

        all_selected = all_sublists_in_list(selected_cryptos_list, filtered_list)
        # all_selected = all(len(selected) == 10 for selected in selected_cryptos_list) if selected_cryptos_list else False
        if update_crypto_tab:
            # check to see if changes were made
            if all_selected != ('select_all' in select_all_checkbox):
                return [["select_all"], True] if all_selected else [[], True]
            return [dash.no_update, False]
        return [["select_all"], True] if all_selected else [[], True]

    # Callback to return selected cryptos when Confirm is clicked
    @app.callback(
        Output("confirm-button", "children"),  # Temporary Output (Replace with backend logic)
        Input("confirm-button", "n_clicks"),
        State({"type": "crypto-checklist", "index": dash.ALL}, "value"),
        prevent_initial_call=True
    )
    def confirm_selection(n_clicks, selected_cryptos_list):
        selected_cryptos = [crypto for sublist in selected_cryptos_list for crypto in sublist]  # Flatten list
        print("Selected Cryptos:", selected_cryptos)  # Replace with actual backend logic
        return "Confirmed ✅"
