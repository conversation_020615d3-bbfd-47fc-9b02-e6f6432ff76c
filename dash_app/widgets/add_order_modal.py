import dash
import dash_bootstrap_components as dbc
from dash import html, dcc, Input, Output, State, ALL, callback_context as ctx
from datetime import datetime, timezone
import pytz
from decimal import Decimal
import uuid
import json

from models.order import Order, BuySell
from trades_db import TradesDB, get_db_cursor, get_db_connection
import helper

# ================================
# Helper Functions
# ================================

def get_current_datetime_string():
    """Get current date and time in EST timezone formatted for datetime-local input"""
    est = pytz.timezone('America/New_York')
    now = datetime.now(est)
    return now.strftime('%Y-%m-%dT%H:%M')

# ================================
# Modal Layout
# ================================

modal = dbc.Modal([
    dbc.ModalHeader([
        html.Div([
            html.I(className="bi bi-plus-circle me-2", style={"fontSize": "1.5rem", "color": "#0d6efd"}),
            html.Span("Add Order to Trade", style={"fontSize": "1.25rem", "fontWeight": "500"})
        ], className="d-flex align-items-center"),
    ]),
    dbc.ModalBody([
        # Trade Info Display
        dbc.Card([
            dbc.CardHeader(html.H6("Trade Information", className="mb-0")),
            dbc.CardBody([
                html.Div(id="add-order-trade-info", className="mb-3")
            ])
        ], className="mb-4"),
        
        # Order Entry Section
        dbc.Card([
            dbc.CardHeader(html.H6("New Order Details", className="mb-0")),
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Buy/Sell"),
                        dcc.Dropdown(
                            id="add-order-buysell",
                            options=[
                                {"label": "Buy", "value": "Buy"},
                                {"label": "Sell", "value": "Sell"}
                            ],
                            value="Buy",
                            clearable=False
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Order Type"),
                        dcc.Dropdown(
                            id="add-order-type",
                            options=[
                                {"label": "Market", "value": "Market"},
                                {"label": "Limit", "value": "Limit"},
                                {"label": "Stop", "value": "Stop"},
                                {"label": "Stop Limit", "value": "Stop Limit"}
                            ],
                            value="Market",
                            clearable=False
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Quantity"),
                        dbc.Input(
                            id="add-order-quantity",
                            type="number",
                            step="any",
                            placeholder="0.00",
                            required=True
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Price"),
                        dbc.Input(
                            id="add-order-price",
                            type="number",
                            step="any",
                            placeholder="0.00",
                            required=True
                        )
                    ], width=3)
                ], className="mb-3"),
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Date & Time (EST)"),
                        dbc.Input(
                            id="add-order-datetime",
                            type="datetime-local",
                            value=get_current_datetime_string()
                        )
                    ], width=4),
                    dbc.Col([
                        dbc.Label("Fee"),
                        dbc.Input(
                            id="add-order-fee",
                            type="number",
                            step="any",
                            placeholder="0.00",
                            value=0
                        )
                    ], width=2),
                    dbc.Col([
                        dbc.Label("Status"),
                        dcc.Dropdown(
                            id="add-order-status",
                            options=[
                                {"label": "Filled", "value": "Filled"},
                                {"label": "Open", "value": "Open"},
                                {"label": "Cancelled", "value": "Cancelled"}
                            ],
                            value="Filled",
                            clearable=False
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Reduce Only"),
                        dbc.Checklist(
                            id="add-order-reduce",
                            options=[{"label": "", "value": True}],
                            value=[],
                            inline=True,
                            style={"marginTop": "8px"}
                        )
                    ], width=3)
                ])
            ])
        ]),
        
        # Alert for validation messages
        html.Div(id="add-order-alert", className="mt-3"),
        
        # Hidden store for trade ID
        dcc.Store(id="add-order-trade-id", data=None)
    ]),
    dbc.ModalFooter([
        dbc.Button("Cancel", id="cancel-add-order", color="secondary", className="me-2"),
        dbc.Button("Add Order", id="save-add-order", color="primary")
    ])
], id="add-order-modal", size="lg", is_open=False, className="add-order-modal")

# ================================
# Callback Functions
# ================================

def get_callbacks(app):
    """Register all callbacks for the add order modal"""
    
    @app.callback(
        [Output("add-order-modal", "is_open"),
         Output("add-order-trade-id", "data"),
         Output("add-order-trade-info", "children")],
        [Input({"type": "add-order-btn", "index": ALL}, "n_clicks"),
         Input("cancel-add-order", "n_clicks")],
        [State("add-order-modal", "is_open"),
         State("add-order-trade-id", "data")],
        prevent_initial_call=True
    )
    def toggle_add_order_modal(add_clicks_list, cancel_clicks, is_open, current_trade_id):
        """Toggle the add order modal and set trade information"""
        ctx = dash.callback_context
        if not ctx.triggered:
            return is_open, current_trade_id, dash.no_update
        
        triggered_id = ctx.triggered[0]["prop_id"]
        
        # Handle cancel button
        if "cancel-add-order" in triggered_id:
            return False, None, ""
        
        # Handle add order button clicks
        if any(add_clicks_list):
            # Extract trade ID from the button that was clicked
            button_data = json.loads(triggered_id.split(".")[0])
            trade_id = button_data["index"]
            
            # Get trade information
            trade = TradesDB.get_trade_by_id(trade_id=trade_id)
            if trade:
                trade_info = html.Div([
                    html.P([html.Strong("Symbol: "), trade.symbol]),
                    html.P([html.Strong("Direction: "), trade.direction.value]),
                    html.P([html.Strong("Exchange: "), trade.exchange.value]),
                    html.P([html.Strong("Current Orders: "), str(len(trade.trade_orders))])
                ])
                return True, trade_id, trade_info
        
        return is_open, current_trade_id, dash.no_update
    
    @app.callback(
        [Output("add-order-alert", "children"),
         Output("add-order-modal", "is_open", allow_duplicate=True)],
        Input("save-add-order", "n_clicks"),
        [State("add-order-trade-id", "data"),
         State("add-order-buysell", "value"),
         State("add-order-type", "value"),
         State("add-order-quantity", "value"),
         State("add-order-price", "value"),
         State("add-order-datetime", "value"),
         State("add-order-fee", "value"),
         State("add-order-status", "value"),
         State("add-order-reduce", "value")],
        prevent_initial_call=True
    )
    def save_order_to_trade(n_clicks, trade_id, buysell, order_type, quantity, price,
                           order_datetime, fee, status, reduce):
        """Save the new order to the existing trade"""
        if not n_clicks or not trade_id:
            return dash.no_update, dash.no_update
        
        try:
            # Validate required fields
            if not quantity or not price:
                return dbc.Alert("Quantity and Price are required", color="danger"), dash.no_update

            # Get the existing trade
            trade = TradesDB.get_trade_by_id(trade_id=trade_id)
            if not trade:
                return dbc.Alert("Trade not found", color="danger"), dash.no_update
            
            # Parse datetime
            est = pytz.timezone('America/New_York')
            dt = datetime.strptime(order_datetime, '%Y-%m-%dT%H:%M')
            dt_est = est.localize(dt)
            dt_utc = dt_est.astimezone(timezone.utc)
            
            # Create new order
            new_order = Order(
                id_field=None,
                order_id=f"MANUAL_ADD_{str(uuid.uuid4())}",
                trade_id=trade_id,
                created_date=dt_utc,
                filled_date=dt_utc if status == "Filled" else None,
                symbol=trade.symbol,
                orderType=order_type,
                orderStatus=status,
                buySell=BuySell.BUY if buysell == "Buy" else BuySell.SELL,
                reduce=bool(reduce),
                price=Decimal(str(price)),
                fillPrice=Decimal(str(price)) if status == "Filled" else Decimal(0),
                fee=Decimal(str(fee or 0)),
                quantity=Decimal(str(quantity)),
                filledQuantity=Decimal(str(quantity)) if status == "Filled" else Decimal(0),
                sierraActivity=None,
                coinbaseOrder=None,
                coinbaseFill=None,
                bybitOrder=None
            )
            
            # Add order to trade and update trade details
            trade.trade_orders.append(new_order)
            trade.update_trade_details()
            
            # Save to database
            cursor = get_db_cursor()
            TradesDB.updateTrade(trade, cursor)
            get_db_connection().commit()

            return dbc.Alert("Order added successfully!", color="success"), False

        except Exception as e:
            return dbc.Alert(f"Error adding order: {str(e)}", color="danger"), dash.no_update
    
    @app.callback(
        [Output("add-order-buysell", "value"),
         Output("add-order-type", "value"),
         Output("add-order-quantity", "value"),
         Output("add-order-price", "value"),
         Output("add-order-datetime", "value"),
         Output("add-order-fee", "value"),
         Output("add-order-status", "value"),
         Output("add-order-reduce", "value"),
         Output("add-order-alert", "children", allow_duplicate=True)],
        Input("add-order-modal", "is_open"),
        prevent_initial_call=True
    )
    def reset_add_order_form(is_open):
        """Reset form fields when modal is opened"""
        if is_open:
            return ("Buy", "Market", None, None, get_current_datetime_string(), 0, "Filled", [], "")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
