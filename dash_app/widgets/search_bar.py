import json
from datetime import datetime, timedelta

import dash
import dash_bootstrap_components as dbc
from dash import html
from dash.dependencies import Input, Output, State

import helper
from dash_app.widgets import symbol_search, date_picker, select_account
from dash_app.widgets.date_picker import date_ranges


# ================================
# Helper Functions
# ================================


def format_date_range(start_date, end_date):
    if start_date is None or end_date is None:
        return dash.no_update

    # Parse dates directly without timezone conversion
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
    
    # Helper function for ordinal indicators (1st, 2nd, 3rd, etc.)
    def get_ordinal(day):
        if 10 <= day % 100 <= 20:
            suffix = 'th'
        else:
            suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
        return f"{day}{suffix}"
    
    # Format with full month name and ordinal day
    start_str = f"{start_date_obj.strftime('%B')} {get_ordinal(start_date_obj.day)}"
    end_str = f"{end_date_obj.strftime('%B')} {get_ordinal(end_date_obj.day)}"
    
    return f"{start_str} - {end_str} ▼"


# ================================
# Dash Layout
# ================================

def get_modals():
    return [
        select_account.modal,
        symbol_search.modal,
        date_picker.modal,
    ]


def get_search_ui():
    # Main Card UI
    return dbc.Card(
        dbc.CardBody([
            dbc.Row([
                # Account Dropdown (Opens Modal)
                dbc.Col(dbc.Button("All Accounts ▼", id="open-account", className="w-100 text-start"),
                        width=10),
                dbc.Col(dbc.Button(html.I(className="bi bi-filter"), color="primary", className="w-100"), width=2),
            ], className="mb-2"),

            dbc.Row([
                # Symbol Dropdown (Opens Modal)
                dbc.Col(dbc.Button("All Symbols ▼", id="open-symbol", className="w-100 text-start"),
                        width=5),
                # Date Range Dropdown (Opens Modal)
                dbc.Col(dbc.Button(id="open-date-modal-button", className="w-100 text-start",
                                  style={"min-height": "38px"}),
                        width=7),
            ])
        ]),
        className="shadow-sm"  #, style={"backgroundColor": "#1E1E24", "borderRadius": "10px", "padding": "10px"} # DARK BAKGROUND
    )


item = html.Div([get_search_ui()] + get_modals())


# ================================
# Dash Callbacks
# ================================

def get_search_bar_callbacks(app):
    select_account.get_callbacks(app)
    symbol_search.get_callbacks(app)
    date_picker.get_callbacks(app)

    @app.callback(
        Output("modal-account", "is_open"),
        [Input("open-account", "n_clicks"),
         Input("confirm-account", "n_clicks"),
         Input("cancel-account", "n_clicks")],
        [State("modal-account", "is_open")],
        prevent_initial_call=True
    )
    def toggle_exchange_accounts_modal(open_click, confirm_click, cancel_click, is_open):
        if open_click or confirm_click or cancel_click:
            return not is_open
        return is_open

    @app.callback(
        Output("modal-symbol", "is_open"),
        Output("trade-search", "n_clicks", allow_duplicate=True),
        [Input("open-symbol", "n_clicks"),
         Input("close-symbol", "n_clicks")],
        State("modal-symbol", "is_open"),
        prevent_initial_call=True
    )
    def toggle_symbol_selection_modal(open_click, close_click, is_open):
        return (not is_open,
                2 if is_open else dash.no_update)

    @app.callback(
        Output("date-picker-modal", "is_open"),
        Output("open-date-modal-button", "children"),
        Output("trade-search", "n_clicks"),
        [Input("open-date-modal-button", "n_clicks"),
         Input("confirm-date", "n_clicks"),
         Input("cancel-date", "n_clicks")],
        [State("date-picker-modal", "is_open"),
         State("selected-dates", "data")],
        prevent_initial_call="initial_duplicate"
    )
    def toggle_date_selection_modal(open_click, confirm_click, cancel_click, is_open, selected_dates):
        ctx = dash.callback_context
        start_date, end_date = selected_dates
        # Handle initial call (no trigger)
        if not ctx.triggered:
            # Return: modal closed, formatted date range, no search trigger
            return False, format_date_range(start_date, end_date), dash.no_update

        # Handle triggered events
        clicked_id = ctx.triggered[0]["prop_id"].split(".")[0]
        needs_search = False
        
        if clicked_id != "cancel-date" and is_open:
            needs_search = True

        return (not is_open,
                format_date_range(start_date=start_date, end_date=end_date),
                2 if needs_search else dash.no_update)
