import calendar
import datetime
import json

import dash
import dash_bootstrap_components as dbc
from dash import html, dcc
from dash.dependencies import Input, Output, State, ALL

# ================================
# Data
# ================================

days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
today = datetime.date.today()

# Define date ranges for each selection
date_ranges = {
    "today": (today, today, "Today"),
    "yesterday": (today - datetime.timedelta(days=1), today - datetime.timedelta(days=1), "Yesterday"),
    "this-week": (today - datetime.timedelta(days=today.weekday()), today, "This Week"),
    "last-week": (
        today - datetime.timedelta(days=today.weekday() + 7), today - datetime.timedelta(days=today.weekday() + 1),
        "Last Week"),
    "this-month": (today.replace(day=1), today, "This Month"),
    "last-month": ((today.replace(day=1) - datetime.timedelta(days=1)).replace(day=1),
                   today.replace(day=1) - datetime.timedelta(days=1), "Last Month"),
    "last-7": (today - datetime.timedelta(days=6), today, "Last 7 days"),
    "last-30": (today - datetime.timedelta(days=29), today, "Last 30 days"),
    "last-90": (today - datetime.timedelta(days=89), today, "Last 90 days"),
    "last-365": (today - datetime.timedelta(days=364), today, "Last 365 days"),
    "year-to-date": (datetime.date(today.year, 1, 1), today, "Year to date"),
}


# ================================
# Helper Functions
# ================================

def generate_calendar(title_id, body_id):
    return html.Div([
        html.Div([
            html.Span(id=title_id, className="fw-bold"),
        ], className="d-flex justify-content-center align-items-center mb-2"),
        html.Div(
            [html.Div(day, className="d-inline-block text-center", style={"width": "14%", "fontWeight": "bold"}) for day
             in days]
        ),
        html.Div(id=body_id)
    ])


def generate_highlighted_calendar(calendar_year, calendar_month, highlight_start_date=None, highlight_end_date=None):
    """Generates a calendar with highlighted date range."""
    cal = calendar.monthcalendar(calendar_year, calendar_month)
    highlighted_body = []

    for week in cal:
        week_row = []
        for day in week:
            if day == 0:
                style = {"width": "14%", "height": "40px", "lineHeight": "40px", "visibility": "hidden"}
                week_row.append(html.Div("", className="d-inline-block text-center border", style=style))
            else:
                date_value = datetime.date(calendar_year, calendar_month, day)
                # is_highlighted = highlight_start_date <= date_value <= highlight_end_date if highlight_start_date is not None else False
                is_highlighted = highlight_start_date and highlight_end_date and highlight_start_date <= date_value <= highlight_end_date
                style = {
                    "width": "14%", "height": "40px", "lineHeight": "40px", "cursor": "pointer",
                    "backgroundColor": "#0056b3" if is_highlighted else "transparent",
                    "color": "white" if is_highlighted else "inherit",
                    "borderRadius": "5px" if is_highlighted else "0",
                    "border": "1px solid #ccc",
                }
                date_id = {"type": "day-of-month", "date": date_value.strftime('%Y-%m-%d')}  # Use dictionary ID

                week_row.append(
                    html.Div(str(day) if day != 0 else "",
                             id=date_id,
                             className="d-inline-block text-center border",
                             style=style,
                             n_clicks=0))
        highlighted_body.append(html.Div(week_row, style={"display": "flex", "justifyContent": "space-between"}))

    return highlighted_body


def next_year_month(current_date):
    if current_date.month == 12:  # If December, rollover to January of the next year
        next_month = 1
        next_year = current_date.year + 1
    else:
        next_month = current_date.month + 1
        next_year = current_date.year
    return next_month, next_year


def update_sidebar(selected_id):
    # Generate an updated sidebar list with selected `active=True`
    updated_sidebar = [
        dbc.ListGroupItem(date_ranges.get(item_id)[2], action=True, id=item_id, n_clicks=0,
                          active=(item_id == selected_id))
        for item_id in date_ranges
    ]
    return updated_sidebar


# ================================
# Dash Layout
# ================================
def get_selected_dates(date_range_key):
    start_date, end_date, label = date_ranges.get(date_range_key)
    return start_date, end_date


# Sidebar options (Scrollable list)
sidebar_options = html.Div([
    dcc.Store(id='sidebar-selection', data="last-30"),
    dcc.Store(id='selected-dates', storage_type="local", data=get_selected_dates("last-30")),
    dcc.Store(id='user-dates', storage_type="local", data=None),
    dcc.Store(id='first-run', data=True),

    dbc.ListGroup(
        id="sidebar-list",
        children=update_sidebar("last-30"),
        flush=True,
    )
], style={
    "maxHeight": "400px",
    "overflowY": "auto",
    # "scrollbar-width": "thin",  # Firefox,
    # "scrollbar-color": "gray transparent"
})

# dbc.ListGroupItem("Last 10 trades", action=True, id="last-10-trades"),
# dbc.ListGroupItem("Last 25 trades", action=True, id="last-25-trades"),
# dbc.ListGroupItem("Last 50 trades", action=True, id="last-50-trades"),
# dbc.ListGroupItem("Last 100 trades", action=True, id="last-100-trades"),
# dbc.ListGroupItem("Last 250 trades", action=True, id="last-250-trades"),
# dbc.ListGroupItem("Last 500 trades", action=True, id="last-500-trades"),

# Date range modal
modal = dbc.Modal(
    [
        dbc.ModalHeader(
            html.Div([
                html.I(className="bi bi-calendar-range me-2", style={"fontSize": "1.5rem", "color": "#0d6efd"}),
                html.Span("Select Date Range", style={"fontSize": "1.25rem", "fontWeight": "500"})
            ], className="d-flex align-items-center"),
            className="border-bottom border-primary pb-2"
        ),
        dbc.ModalBody(
            dbc.Row([
                dbc.Col(
                    html.Div([
                        html.Div(
                            html.I(className="bi bi-filter-circle-fill mb-2",
                                   style={"fontSize": "1.5rem", "color": "#0d6efd"}),
                            className="text-center"
                        ),
                        sidebar_options
                    ], className="bg-light rounded p-2 h-100"),
                    width=3, className="border-end"
                ),
                dbc.Col([
                    dbc.Row([
                        dbc.Col(
                            dbc.Button(
                                html.I(className="bi bi-chevron-left"),
                                id="prev-month",
                                color="light",
                                className="w-100 shadow-sm"
                            ),
                            width=2
                        ),
                        dbc.Col(
                            html.Div(
                                f"{calendar.month_name[today.month]} {today.year}",
                                id="calendars-title",
                                className="text-center fw-bold bg-light rounded py-2 shadow-sm"
                            ),
                            width=8
                        ),
                        dbc.Col(
                            dbc.Button(
                                html.I(className="bi bi-chevron-right"),
                                id="next-month",
                                color="light",
                                className="w-100 shadow-sm"
                            ),
                            width=2
                        )
                    ], className="mb-3"),
                    dbc.Row([
                        dbc.Col(
                            html.Div(
                                generate_calendar("calendar-title-1", "calendar-body-1"),
                                className="bg-white rounded p-2 shadow-sm"
                            ),
                            width=6
                        ),
                        dbc.Col(
                            html.Div(
                                generate_calendar("calendar-title-2", "calendar-body-2"),
                                className="bg-white rounded p-2 shadow-sm"
                            ),
                            width=6
                        ),
                    ]),
                    html.Div(
                        dbc.Row([
                            dbc.Col(
                                dbc.Button(
                                    html.Div([
                                        html.I(className="bi bi-x-circle me-2"),
                                        "Cancel"
                                    ], className="d-flex align-items-center justify-content-center"),
                                    id="cancel-date",
                                    color="secondary",
                                    className="w-100 shadow-sm"
                                )
                            ),
                            dbc.Col(
                                dbc.Button(
                                    html.Div([
                                        html.I(className="bi bi-check-circle me-2"),
                                        "Confirm"
                                    ], className="d-flex align-items-center justify-content-center"),
                                    id="confirm-date",
                                    color="primary",
                                    className="w-100 shadow-sm"
                                )
                            )
                        ], className="mt-4")
                    )
                ], width=9)
            ], className="g-3")
        ),
    ],
    id="date-picker-modal",
    is_open=False,
    size="lg",
    className="date-picker-modal"
)


# ================================
# Dash Callbacks
# ================================

def get_callbacks(app):
    # Load stored data on page load
    # app.clientside_callback(
    #     """
    #     function() {
    #         const storedData = window.localStorage.getItem('selected-dates');
    #         if (storedData) {
    #             return JSON.parse(storedData);
    #         }
    #         return {"start_date": "2024-01-01", "end_date": "2024-01-07"};
    #     }
    #     """,
    #     Output("selected-dates", "data"),
    #     Input("selected-dates", "modified_timestamp"),
    #     prevent_initial_call=True
    # )

    @app.callback(
        [Output("calendar-body-1", "children", allow_duplicate=True),
         Output("calendar-body-2", "children", allow_duplicate=True),
         Output("selected-dates", "data", allow_duplicate=True),
         Output("user-dates", "data", allow_duplicate=True),  # Store selected dates for when user goes forward/back
         Output("sidebar-list", "children", allow_duplicate=True),  # Update sidebar to remove selection
         Output("sidebar-selection", "data", allow_duplicate=True)],
        Input({"type": "day-of-month", "date": ALL}, "n_clicks"),  # Dynamically match any clicked date
        State("user-dates", "data"),  # Retrieve previously user-selected dates
        prevent_initial_call=True
    )
    def track_calendar_date_selection(n_clicks, user_dates):
        """Tracks first and second click, highlighting the date range."""
        ctx = dash.callback_context
        # Ignore if auto-triggered from dynamically building the component
        if not ctx.triggered or ctx.triggered[0]["value"] == 0:
            return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update

        clicked_id = json.loads(ctx.triggered[0]["prop_id"].split(".")[0])["date"]  # Extract clicked day
        selected_date = datetime.datetime.strptime(clicked_id, "%Y-%m-%d").date()

        # Retrieve previously selected dates
        start_date, end_date = (
            datetime.datetime.strptime(date_str, "%Y-%m-%d").date() if date_str else None
            for date_str in (user_dates or [None, None])
        )

        if start_date is None:
            start_date = selected_date
            end_date = None  # Reset second click
        elif end_date is None:
            if selected_date < start_date:
                start_date, end_date = selected_date, start_date  # Ensure correct order
            else:
                end_date = selected_date
        else:
            # Reset and start over on the third click
            start_date, end_date = selected_date, None

        next_month, next_year = next_year_month(start_date)
        return (
            generate_highlighted_calendar(start_date.year, start_date.month, start_date,
                                          end_date if end_date is not None else start_date),
            generate_highlighted_calendar(next_year, next_month, start_date,
                                          end_date if end_date is not None else start_date),
            (start_date, end_date) if start_date and end_date else dash.no_update,  # Save selected dates
            (start_date, end_date),
            update_sidebar(None),
            None  # Save sidebar selection as empty (no selection)
        )

    @app.callback(
        [Output("calendars-title", "children", allow_duplicate=True),
         Output("calendar-title-1", "children", allow_duplicate=True),
         Output("calendar-body-1", "children", allow_duplicate=True),
         Output("calendar-title-2", "children", allow_duplicate=True),
         Output("calendar-body-2", "children", allow_duplicate=True),
         Output("sidebar-list", "children", allow_duplicate=True),
         Output("sidebar-selection", "data", allow_duplicate=True),  # Update sidebar list items
         Output("selected-dates", "data", allow_duplicate=True),
         Output("user-dates", "data")],
        [Input("today", "n_clicks"),
         Input("yesterday", "n_clicks"),
         Input("this-week", "n_clicks"),
         Input("last-week", "n_clicks"),
         Input("this-month", "n_clicks"),
         Input("last-month", "n_clicks"),
         Input("last-7", "n_clicks"),
         Input("last-30", "n_clicks"),
         Input("last-90", "n_clicks"),
         Input("last-365", "n_clicks"),
         Input("year-to-date", "n_clicks"),
         State("selected-dates", "data"),
         State("sidebar-selection", "data"),
         State("user-dates", "data")],
        prevent_initial_call=True
    )
    def sidebar_item_selection(*clicks):
        """ Updates the calendar to highlight selected date ranges and update sidebar active state """
        ctx = dash.callback_context
        if not ctx.triggered or ctx.triggered[0]["value"] == 0:
            return (dash.no_update, dash.no_update, dash.no_update, dash.no_update,
                    dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update)

        selected_id = ctx.triggered[0]["prop_id"].split(".")[0]
        if selected_id is None or selected_id == "":
            selected_id = clicks[12]
        start_date, end_date, label = date_ranges.get(selected_id)

        updated_sidebar = update_sidebar(selected_id)
        next_month, next_year = next_year_month(start_date)
        return (
            f"{calendar.month_name[start_date.month]} {start_date.year}",
            f"{calendar.month_name[start_date.month]} {start_date.year}",
            generate_highlighted_calendar(start_date.year, start_date.month, start_date, end_date),
            f"{calendar.month_name[next_month]} {next_year}",
            generate_highlighted_calendar(next_year, next_month, start_date, end_date),
            updated_sidebar,  # Update sidebar to set item active (highlight)
            selected_id,  # Track the selected ID
            (start_date, end_date),
            None,
        )

    # TODO Maybe only load calendar modal if user opens it, currently this is called upon page load
    @app.callback(
        [Output("calendars-title", "children"),
         Output("calendar-title-1", "children"),
         Output("calendar-body-1", "children"),
         Output("calendar-title-2", "children"),
         Output("calendar-body-2", "children")],
        [Input("next-month", "n_clicks"),
         Input("prev-month", "n_clicks")],
        [State("calendars-title", "children"),
         State("sidebar-selection", "data"),
         State("selected-dates", "data")],
        prevent_initial_call=True
    )
    def next_prev_button(next_clicks, prev_clicks, current_title, sidebar_selection, selected_dates):
        # Set Sunday as the first day of the week
        calendar.setfirstweekday(calendar.SUNDAY)
        
        # Determine start_date and end_date based on sidebar selection or user-selected dates
        if sidebar_selection and sidebar_selection in date_ranges:
            # Get from predefined date ranges
            start_date, end_date, label = date_ranges.get(sidebar_selection)
        else:
            # Get from user-selected dates
            try:
                start_date = datetime.datetime.strptime(selected_dates[0], "%Y-%m-%d").date() if selected_dates and selected_dates[0] else None
                end_date = datetime.datetime.strptime(selected_dates[1], "%Y-%m-%d").date() if selected_dates and selected_dates[1] else None
                label = None  # No label for custom date range
            except (TypeError, IndexError, ValueError):
                # Fallback to default date range if there's an issue
                start_date, end_date, label = date_ranges.get("last-30")

        month_str, year = current_title.split()
        month = list(calendar.month_name).index(month_str)  # Convert "March" → 3
        current_date = datetime.date(int(year), month, 1)

        # Check which button was clicked and adjust the date accordingly
        if dash.callback_context.triggered_id == "prev-month":
            current_date = current_date.replace(day=1) - datetime.timedelta(days=1)
            # Now current_date is at the **last day of the previous month**
            current_date = current_date.replace(day=1)  # Set to first day

        elif dash.callback_context.triggered_id == "next-month":
            current_date = current_date.replace(day=28) + datetime.timedelta(days=4)
            # Now current_date is in the **next month**
            current_date = current_date.replace(day=1)  # Set to first day

        cal_body_1 = generate_highlighted_calendar(current_date.year, current_date.month, start_date, end_date)

        next_month, next_year = next_year_month(current_date)
        cal_body_2 = generate_highlighted_calendar(next_year, next_month, start_date, end_date)

        return (f"{calendar.month_name[current_date.month]} {current_date.year}",
                f"{calendar.month_name[current_date.month]} {current_date.year}",
                cal_body_1,
                f"{calendar.month_name[next_month]} {next_year}",
                cal_body_2)

    @app.callback(
        [Output("calendars-title", "children", allow_duplicate=True),
         Output("calendar-title-1", "children", allow_duplicate=True),
         Output("calendar-body-1", "children", allow_duplicate=True),
         Output("calendar-title-2", "children", allow_duplicate=True),
         Output("calendar-body-2", "children", allow_duplicate=True),
         Output("sidebar-list", "children"),
         Output("sidebar-selection", "data")],
        Input("first-run", "data"),
        [State("selected-dates", "data"),
         State("sidebar-selection", "data"),
         State("user-dates", "data")],
        prevent_initial_call='initial_duplicate'
    )
    def initial_setup(*clicks):
        if bool(clicks[0]):
            start_date, end_date, label = date_ranges.get(clicks[2])
            updated_sidebar = update_sidebar(clicks[2])
            next_month, next_year = next_year_month(start_date)
            return (
                f"{calendar.month_name[start_date.month]} {start_date.year}",
                f"{calendar.month_name[start_date.month]} {start_date.year}",
                generate_highlighted_calendar(start_date.year, start_date.month, start_date, end_date),
                f"{calendar.month_name[next_month]} {next_year}",
                generate_highlighted_calendar(next_year, next_month, start_date, end_date),
                updated_sidebar,  # Update sidebar to set item active (highlight)
                clicks[2],  # Track the selected ID
            )
        return (dash.no_update, dash.no_update, dash.no_update, dash.no_update,
                dash.no_update, dash.no_update, dash.no_update)