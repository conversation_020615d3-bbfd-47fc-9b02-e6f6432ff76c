import dash_bootstrap_components as dbc
from dash import html, dcc

import helper


def get_total_trades_card(total_trades, wins, losses):
    card = dbc.Card(
        dbc.CardBody([
            dbc.Row([
                # Icon (if needed)
                dbc.Col(html.I(className="bi-arrow-left-right", style={"fontSize": "1.75rem"}), width="auto"),
                # Content
                trade_wins_losses_row("Total Trades", total_trades, str(wins) + "W", str(losses) + "L")
            ], align="center", className="m-0 p-0")
        ], className="d-flex flex-column justify-content-center"),
        className="shadow-sm h-100",
        style={"borderRadius": "10px", "padding": "10px"}
    )
    return card


def get_win_rate_card(win_rate, total_trades, wins, losses):
    card = dbc.Card(
        dbc.CardBody([
            dbc.Row([
                dbc.Col(dcc.Graph(
                    figure={
                        "data": [
                            {
                                "values": [wins, losses] if (wins > 0 or losses > 0) else [1, 1],  # Default 50/50 when no data
                                "labels": ["Wins", "Losses"],
                                "marker": {"colors": ["#1E90FF", "#D3D3D3"] if (wins > 0 or losses > 0) else ["#E0E0E0", "#E0E0E0"]},  # Gray for empty state
                                "type": "pie",
                                "hole": 0.65,
                                "showlegend": False
                            }
                        ],
                        "layout": {
                            "margin": {"l": 0, "r": 0, "t": 0, "b": 0},
                            "height": 45,
                            "width": 45,
                            "paper_bgcolor": "rgba(0,0,0,0)",  # Makes the entire figure background transparent
                            "plot_bgcolor": "rgba(0,0,0,0)",  # Makes the plot area background transparent
                            "annotations": [
                                {
                                    "text": "N/A" if (wins == 0 and losses == 0) else "",
                                    "showarrow": False,
                                    "font": {"size": 10, "color": "#888888"}
                                }
                            ] if (wins == 0 and losses == 0) else []
                        }
                    } ,
                    config={"displayModeBar": False},
                    style={"marginTop": "-10px"}
                ), width="auto"),
                # Calculate win rate
                trade_wins_losses_row("Win Rate",
                                      str(round((wins / total_trades) * 100)) + "%" if total_trades > 0 else 0,
                                      str(wins) + "W",
                                      str(losses) + "L")
                # Ensures no space between "Total Trades" and numbers
            ], align="center"),

            dbc.Row([
                html.Table(
                    [
                        html.Tr([
                            html.Td([
                                html.Div([
                                    html.Span("0 Breakeven", className="text-start text-muted",
                                              style={"fontSize": "12px"}),  # Left-aligned text
                                    html.Span([
                                        html.Span("Set Breakeven Filter", className="text-muted text-end",
                                                  style={"fontSize": "12px"}),
                                        html.I(className="bi bi-filter ms-1 text-end", style={"fontSize": "1rem"})
                                    ], className="d-flex align-items-center")  # Right-aligned text + icon
                                ], className="d-flex justify-content-between w-100 align-items-center")
                            ])

                        ])
                    ]
                )
            ], justify="between")
        ], className="m-0 p-0 pt-2 ps-3 pe-3 d-flex flex-column justify-content-center"),
        className="shadow-sm h-100",
        style={"borderRadius": "10px", "padding": "10px"}
    )

    return card


def get_average_trade_time_card(average_duration):
    avg_duration = helper.format_duration(average_duration)
    if avg_duration == "":
        avg_duration = "0h 0m"
    card = dbc.Card(
        dbc.CardBody([
            dbc.Row([
                dbc.Col(html.I(className="bi bi-graph-up", style={"fontSize": "24px", "color": "#3b82f6"}),
                        width="auto"),
                dbc.Col([
                    html.Div("Avg Trade Duration", className="text-muted", style={"fontSize": "12px"}),
                    html.H4([
                        html.Span(avg_duration, className="fw-bold"),
                    ], className="mb-0")
                ])
            ], align="center")
        ], className="d-flex flex-column justify-content-center"),
        className="shadow-sm h-100",
        style={"borderRadius": "10px", "padding": "10px"}
    )

    return card


def get_long_short_ratio_card(longs_percentage, shorts_percentage, longs_count, shorts_count):
    card = dbc.Card(
        dbc.CardBody([
            dbc.Row([
                dbc.Col([
                    html.Div("Longs Ratio", className="text-muted", style={"fontSize": "12px"}),
                    html.Div([
                        html.Span(f"{longs_percentage}%", className="fw-bold text-success"),
                        html.Span(f" ({longs_count})", className="text-success"),
                    ], className="mb-1")
                ], width=6),
                dbc.Col([
                    html.Div("Shorts Ratio", className="text-muted text-end", style={"fontSize": "12px"}),
                    html.Div([
                        html.Span(f"{shorts_percentage}%", className="fw-bold text-danger"),
                        html.Span(f" ({shorts_count})", className="text-danger"),
                    ], className="mb-1 text-end")
                ], width=6),
            ]),
            dbc.Progress(
                children=[
                    dbc.Progress(value=longs_percentage, color="success", bar=True,
                                 style={"borderTopLeftRadius": "10px",
                                        "borderBottomLeftRadius": "10px",
                                        "borderTopRightRadius": "0px",
                                        "borderBottomRightRadius": "0px", }),
                    dbc.Progress(value=shorts_percentage, color="danger", bar=True,
                                 style={"borderTopLeftRadius": "0px",
                                        "borderBottomLeftRadius": "0px",
                                        "borderTopRightRadius": "10px",
                                        "borderBottomRightRadius": "10px", }),
                ],
                style={"height": "8px", "borderRadius": "10px"}
            )
        ], className="d-flex flex-column justify-content-center"),
        className="shadow-sm h-100",
        style={"borderRadius": "10px", "padding": "10px"}
    )

    return card


def trade_wins_losses_row(title, win_rate, wins, losses):
    return dbc.Col([
        # Total Trades Title (Tightly Aligned)
        html.P(title, className="text-muted m-0 p-0", style={"line-height": "1"}),

        # Row for Number & Win/Loss
        dbc.Row(
            [
                html.Table(
                    [
                        html.Tr([
                            html.Td(html.H3(str(win_rate), className="fw-bold m-0 p-0"),
                                    className="m-0 p-0 pe-1"),
                            # Less padding

                            html.Td(html.Span([
                                html.Span(wins, className="text-success fw-bold fs-7"),
                                html.Span("/", className="fw-bold fs-8"),
                                html.Span(losses, className="text-danger fw-bold fs-7"),
                            ]), style={"white-space": "nowrap", "vertical-align": "top"})
                        ])
                    ],
                    className="m-0 p-0",  # Ensure no extra padding/margin in table
                    style={"display": "inline-block"}
                )
            ],
            align="start",  # Avoids unnecessary centering that adds space
            justify="start",  # No forced extra spacing
            className="m-0 p-0"  # No margin/padding for the row
        ),
    ], className="d-flex flex-column gap-0")
