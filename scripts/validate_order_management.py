#!/usr/bin/env python3
"""
Order Management Validation Script

Validates the comprehensive order management implementation for Bybit futures trading.
Checks all components and their integration with the TradeCraft system.
"""

import sys
import os
import logging
from decimal import Decimal

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def validate_imports():
    """Validate that all required modules can be imported"""
    logger.info("🔍 Validating imports...")
    
    try:
        # Core services
        from services.bybit import Bybit
        from services.bybit_config import get_config, setup_testnet, setup_mainnet
        from services.order_execution_service import OrderExecutionService
        from services.api_utils import BybitAPIError, BybitRateLimits
        from services.bybit_websocket_service import BybitWebSocketService
        
        # Models
        from models.trade import Trade, TradeStatus
        from models.order import Order, BuySell
        
        # Trade creation
        from services.trade_creation_service import TradeCreationService
        
        logger.info("✅ All imports successful")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False


def validate_configuration():
    """Validate configuration system"""
    logger.info("🔧 Validating configuration system...")
    
    try:
        # Test environment switching
        testnet_config = setup_testnet()
        assert testnet_config.is_testnet, "Testnet config should be testnet"
        
        mainnet_config = setup_mainnet()
        assert mainnet_config.is_mainnet, "Mainnet config should be mainnet"
        
        # Test configuration properties
        config = get_config()
        assert config.credentials is not None, "Credentials should be available"
        assert config.endpoints is not None, "Endpoints should be available"
        
        # Test headers
        headers = config.get_headers()
        assert "x-api-token" in headers, "Headers should contain API token"
        
        logger.info("✅ Configuration system working")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration error: {e}")
        return False


def validate_api_utils():
    """Validate API utilities (rate limiting, error handling)"""
    logger.info("🛡️ Validating API utilities...")
    
    try:
        # Test rate limiter
        from services.api_utils import RateLimiter
        limiter = RateLimiter(max_calls=5, time_window=1)
        
        # Should allow initial calls
        assert limiter.is_allowed(), "Should allow initial calls"
        
        # Fill up the limit
        for _ in range(5):
            limiter.add_call()
        
        # Should be rate limited now
        assert not limiter.is_allowed(), "Should be rate limited"
        assert limiter.wait_time() > 0, "Should have wait time"
        
        # Test error handling
        from services.api_utils import APIErrorHandler, BybitAPIError
        
        # Test error code mapping
        assert "10001" in APIErrorHandler.ERROR_CODES, "Should have error code mapping"
        
        logger.info("✅ API utilities working")
        return True
        
    except Exception as e:
        logger.error(f"❌ API utilities error: {e}")
        return False


def validate_order_methods():
    """Validate order management methods exist and have correct signatures"""
    logger.info("📋 Validating order management methods...")
    
    try:
        from services.bybit import Bybit
        
        # Check core order methods exist
        methods_to_check = [
            'submit_order',
            'submit_stop_order', 
            'get_order_status',
            'get_active_orders',
            'update_order',
            'cancel_order',
            'cancel_all_orders',
            'get_order_history'
        ]
        
        for method_name in methods_to_check:
            assert hasattr(Bybit, method_name), f"Missing method: {method_name}"
            method = getattr(Bybit, method_name)
            assert callable(method), f"Method {method_name} is not callable"
        
        # Check trade management methods
        trade_methods = [
            'submit_bracket_order',
            'submit_entry_order',
            'submit_stop_loss',
            'submit_take_profit',
            'close_position'
        ]
        
        for method_name in trade_methods:
            assert hasattr(Bybit, method_name), f"Missing trade method: {method_name}"
        
        logger.info("✅ Order management methods available")
        return True
        
    except Exception as e:
        logger.error(f"❌ Order methods error: {e}")
        return False


def validate_order_execution_service():
    """Validate order execution service"""
    logger.info("⚡ Validating order execution service...")
    
    try:
        from services.order_execution_service import OrderExecutionService, OrderExecutionResult
        
        # Check methods exist
        assert hasattr(OrderExecutionService, 'execute_setup_trade'), "Missing execute_setup_trade"
        assert hasattr(OrderExecutionService, 'execute_fibonacci_trade'), "Missing execute_fibonacci_trade"
        
        # Test result classes
        result = OrderExecutionResult(success=True, order_id="test_123")
        assert result.success == True, "OrderExecutionResult should work"
        assert result.order_id == "test_123", "OrderExecutionResult should store order_id"
        
        logger.info("✅ Order execution service available")
        return True
        
    except Exception as e:
        logger.error(f"❌ Order execution service error: {e}")
        return False


def validate_websocket_service():
    """Validate WebSocket service"""
    logger.info("🌐 Validating WebSocket service...")
    
    try:
        from services.bybit_websocket_service import BybitWebSocketService, get_websocket_service
        
        # Test service creation
        ws_service = get_websocket_service()
        assert ws_service is not None, "WebSocket service should be created"
        
        # Test handler methods
        assert hasattr(ws_service, 'add_order_handler'), "Missing add_order_handler"
        assert hasattr(ws_service, 'add_position_handler'), "Missing add_position_handler"
        
        # Test connection status
        status = ws_service.get_connection_status()
        assert isinstance(status, dict), "Status should be a dictionary"
        assert 'connected' in status, "Status should have connected field"
        
        logger.info("✅ WebSocket service available")
        return True
        
    except Exception as e:
        logger.error(f"❌ WebSocket service error: {e}")
        return False


def validate_trade_integration():
    """Validate integration with TradeCraft Trade system"""
    logger.info("🔗 Validating TradeCraft integration...")
    
    try:
        from models.trade import Trade, TradeStatus
        from models.order import Order, BuySell
        from services.trade_creation_service import OrderType, OrderStatus
        
        # Test getTotalQuantity modification for SETUP trades
        # Create a test trade with SETUP status
        test_order = Order(
            id_field=None,
            order_id="test_order",
            trade_id=None,
            created_date=None,
            filled_date=None,
            symbol="BTCUSDT",
            orderType=OrderType.ENTRY,
            orderStatus=OrderStatus.SETUP,
            buySell=BuySell.BUY,
            reduce=False,
            price=Decimal("50000"),
            fillPrice=Decimal("0"),
            fee=Decimal("0"),
            quantity=Decimal("0.1"),      # Planned quantity
            filledQuantity=Decimal("0"),  # No fills yet
            sierraActivity=None,
            coinbaseOrder=None,
            coinbaseFill=None,
            bybitOrder=None
        )
        
        # Create trade with SETUP status
        setup_trade = Trade(
            id_field=None,
            exchange_trade_id="test_trade",
            trade_orders=[test_order],
            unfilled_orders=[],
            symbol="BTCUSDT",
            accountBalance=Decimal("10000"),
            exchange=None,
            direction=None,
            timeOpen=None,
            status=TradeStatus.SETUP,  # SETUP status
            tradeQty=None,
            openQty=None,
            lastUpdate=None,
            timeClose=None,
            duration=None,
            chartLink=None,
            notes=None,
            notional=None,
            leverage=None,
            avgOpenPrice=None,
            avgClosePrice=None,
            riskAmt=None,
            riskPercent=None,
            profit=Decimal("0"),
            fees=None,
            strategy=None,
            time_frame=None,
            username="test"
        )
        
        # Test that SETUP trades use quantity instead of filledQuantity
        total_qty = setup_trade.getTotalQuantity()
        assert total_qty == Decimal("0.1"), f"SETUP trade should use quantity (0.1), got {total_qty}"
        
        # Test that OPEN trades use filledQuantity
        setup_trade.status = TradeStatus.OPEN
        total_qty = setup_trade.getTotalQuantity()
        assert total_qty == Decimal("0"), f"OPEN trade should use filledQuantity (0), got {total_qty}"
        
        logger.info("✅ TradeCraft integration working")
        return True
        
    except Exception as e:
        logger.error(f"❌ TradeCraft integration error: {e}")
        return False


def validate_chart_api_integration():
    """Validate chart API integration"""
    logger.info("📊 Validating chart API integration...")
    
    try:
        # Check that chart_api.py has been updated with order execution
        import services.chart_api
        
        # Read the chart_api.py file to check for our integration
        chart_api_file = services.chart_api.__file__
        with open(chart_api_file, 'r') as f:
            content = f.read()
        
        # Check for our integration code
        assert 'OrderExecutionService' in content, "Chart API should import OrderExecutionService"
        assert 'execute_setup_trade' in content, "Chart API should call execute_setup_trade"
        assert 'execution_status' in content, "Chart API should include execution status"
        
        logger.info("✅ Chart API integration present")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chart API integration error: {e}")
        return False


def run_validation():
    """Run all validation checks"""
    logger.info("🚀 Starting comprehensive order management validation...")
    
    checks = [
        ("Imports", validate_imports),
        ("Configuration", validate_configuration),
        ("API Utils", validate_api_utils),
        ("Order Methods", validate_order_methods),
        ("Order Execution Service", validate_order_execution_service),
        ("WebSocket Service", validate_websocket_service),
        ("TradeCraft Integration", validate_trade_integration),
        ("Chart API Integration", validate_chart_api_integration)
    ]
    
    results = {}
    for check_name, check_func in checks:
        logger.info(f"\n--- {check_name} ---")
        try:
            results[check_name] = check_func()
        except Exception as e:
            logger.error(f"❌ {check_name} failed with exception: {e}")
            results[check_name] = False
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("VALIDATION SUMMARY")
    logger.info("="*50)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{check_name:.<30} {status}")
        if result:
            passed += 1
    
    logger.info("-"*50)
    logger.info(f"TOTAL: {passed}/{total} checks passed")
    
    if passed == total:
        logger.info("🎉 ALL VALIDATIONS PASSED! Order management system is ready.")
        return True
    else:
        logger.error(f"⚠️ {total - passed} validation(s) failed. Please review and fix issues.")
        return False


if __name__ == "__main__":
    success = run_validation()
    sys.exit(0 if success else 1)
