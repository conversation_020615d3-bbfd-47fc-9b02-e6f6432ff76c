@echo off
set SERVER=root@128.199.252.188
set REMOTE_PATH=/home/<USER>/

REM ⚠️ Update these paths to match your system
set LOCAL_MAIN=C:\Users\<USER>\PycharmProjects\Journal\services\main.py

echo 📤 Uploading files to server...
scp "%LOCAL_MAIN%" %SERVER%:%REMOTE_PATH%

echo 🔁 Restarting FastAPI service...
ssh %SERVER% "sudo systemctl restart fastapi-bybit"

echo ✅ Checking service status...
ssh %SERVER% "systemctl status fastapi-bybit --no-pager -l"

echo 📄 Following logs (Ctrl+C to exit)...
ssh %SERVER% "journalctl -u fastapi-bybit -f"