#!/usr/bin/env python3
"""
Standalone test script for the get_or_create_strategy_by_name function.
This version includes all necessary helper functions to avoid import dependencies.
"""

import sqlite3
import os
from datetime import datetime
import pytz

# Database path (same as in trades_db.py)
DB_PATH = os.path.join(os.path.dirname(__file__), 'trades.db')


def get_now_date():
    """Get current date in New York timezone."""
    # Get current time in UTC
    utc_now = datetime.utcnow()
    
    # Localize to UTC properly
    utc_aware = pytz.utc.localize(utc_now)
    
    # Convert to New York timezone
    local_tz = pytz.timezone("America/New_York")
    local_date = utc_aware.astimezone(local_tz)
    
    return local_date


def date_to_ms(date, multiplier=1000):
    """Convert date to milliseconds timestamp."""
    if date is None:
        return None
    return int(date.timestamp() * multiplier)


def get_db_connection():
    """Get a database connection."""
    return sqlite3.connect(DB_PATH, check_same_thread=False)


def get_or_create_strategy_by_name(strategy_name):
    """
    Looks up a strategy by name. If not found, creates a new strategy record.
    
    Args:
        strategy_name (str): The name of the strategy to find or create
        
    Returns:
        int: The strategy ID (either existing or newly created)
        
    Raises:
        Exception: If database operation fails
    """
    if not strategy_name or not strategy_name.strip():
        raise ValueError("Strategy name cannot be empty")
        
    strategy_name = strategy_name.strip()
    connection = get_db_connection()
    cursor = connection.cursor()
    
    try:
        # First, try to find existing strategy by name
        cursor.execute("SELECT id FROM Strategy WHERE name = ?", (strategy_name,))
        row = cursor.fetchone()
        
        if row:
            # Strategy exists, return its ID
            strategy_id = row[0]
            print(f"✅ Found existing strategy '{strategy_name}' with ID: {strategy_id}")
            return strategy_id
        else:
            # Strategy doesn't exist, create a new one
            current_time = get_now_date()
            current_time_ms = date_to_ms(current_time)
            
            cursor.execute('''
                INSERT INTO Strategy (name, display_name, description, notes, images, createdDate, modifiedDate, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                strategy_name,
                strategy_name,  # Use name as display_name by default
                "",  # Empty description
                "",  # Empty notes
                "",  # Empty images
                current_time_ms,
                current_time_ms,
                True  # Active by default
            ))
            
            strategy_id = cursor.lastrowid
            connection.commit()
            
            print(f"✅ Created new strategy '{strategy_name}' with ID: {strategy_id}")
            return strategy_id
            
    except Exception as e:
        print(f"❌ Error in get_or_create_strategy_by_name for '{strategy_name}': {e}")
        raise
    finally:
        connection.close()


def test_strategy_lookup_or_create():
    """Test the strategy lookup and creation functionality."""
    
    print("🧪 Testing Strategy Lookup/Create Functionality")
    print("=" * 50)
    
    # Check if database exists
    if not os.path.exists(DB_PATH):
        print(f"❌ Database not found at: {DB_PATH}")
        print("   Please make sure the database exists and has been initialized.")
        return
    
    # Test cases
    test_strategies = [
        "Breakout Strategy",
        "Mean Reversion", 
        "Momentum Play",
        "Support/Resistance",
        "Breakout Strategy"  # Duplicate to test lookup
    ]
    
    strategy_ids = {}
    
    for strategy_name in test_strategies:
        print(f"\n📋 Processing strategy: '{strategy_name}'")
        
        try:
            # Use the function to get or create the strategy
            strategy_id = get_or_create_strategy_by_name(strategy_name)
            strategy_ids[strategy_name] = strategy_id
            
            print(f"   ➡️ Strategy ID: {strategy_id}")
            
        except Exception as e:
            print(f"   ❌ Error processing '{strategy_name}': {e}")
    
    print(f"\n📊 Summary:")
    print(f"   Total strategies processed: {len(test_strategies)}")
    print(f"   Unique strategy IDs obtained: {len(set(strategy_ids.values()))}")
    
    # Display all strategy IDs
    print(f"\n📋 Strategy ID Mapping:")
    for name, strategy_id in strategy_ids.items():
        print(f"   '{name}' → ID: {strategy_id}")
    
    # Verify strategies were created/found by querying database directly
    print(f"\n🔍 Verifying strategies in database:")
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT id, name, display_name FROM Strategy ORDER BY id")
        strategies = cursor.fetchall()
        connection.close()
        
        print(f"   Total strategies in database: {len(strategies)}")
        for strategy in strategies:
            print(f"   - ID: {strategy[0]}, Name: '{strategy[1]}', Display: '{strategy[2]}'")
    except Exception as e:
        print(f"   ❌ Error retrieving strategy list: {e}")


def test_error_handling():
    """Test error handling for edge cases."""
    
    print(f"\n🚨 Testing Error Handling")
    print("=" * 30)
    
    # Test empty strategy name
    test_cases = [
        "",           # Empty string
        "   ",        # Whitespace only
        None,         # None value
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing with: {repr(test_case)}")
        try:
            strategy_id = get_or_create_strategy_by_name(test_case)
            print(f"   ⚠️ Unexpected success - ID: {strategy_id}")
        except ValueError as e:
            print(f"   ✅ Correctly caught ValueError: {e}")
        except Exception as e:
            print(f"   ❌ Unexpected error type: {type(e).__name__}: {e}")


def example_usage():
    """Show example usage in trade processing context."""
    
    print(f"\n💼 Example Usage in Trade Processing")
    print("=" * 40)
    
    # Simulate processing a trade with a strategy name
    trade_strategy_name = "Fibonacci Retracement"
    
    print(f"📈 Processing trade with strategy: '{trade_strategy_name}'")
    
    try:
        # Step 1: Ensure the strategy exists (create if needed)
        strategy_id = get_or_create_strategy_by_name(trade_strategy_name)
        
        # Step 2: Now you can safely use this strategy_id when creating/updating trades
        print(f"✅ Strategy ready for trade association - ID: {strategy_id}")
        
        print(f"\n💡 In your trade processing code, you would now:")
        print(f"   1. Set trade.strategy = '{strategy_id}' (or add to existing strategy list)")
        print(f"   2. Save the trade with TradesDB.saveTrade(trade)")
        print(f"   3. Or use TradesDB.save_trade_strategy(['{trade_strategy_name}'], trade_id=trade_id)")
        
    except Exception as e:
        print(f"❌ Error in trade processing example: {e}")


if __name__ == "__main__":
    print("🚀 Starting Strategy Lookup/Create Tests")
    print("=" * 60)
    
    try:
        # Run the main test
        test_strategy_lookup_or_create()
        
        # Test error handling
        test_error_handling()
        
        # Show example usage
        example_usage()
        
        print(f"\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
