from datetime import timedelta

import helper


def get_strategy_summary():
    return "strat summary"


class Strategy:
    def __init__(self, strat_id, name, display_name, description, sort_order, is_active, notes, images, created_date,
                 modified_date, username):
        self.strat_id = strat_id
        self.name = name
        self.display_name = display_name
        self.description = description
        self.sort_order = sort_order
        self.is_active = is_active
        self.notes = notes
        self.images = images
        self.created_date = created_date
        self.modified_date = modified_date
        self.username = username

    @classmethod
    def fromRow(cls, row):
        return cls(strat_id=row.get("id"),
                   name=row.get("name"),
                   display_name=row.get("display_name"),
                   description=row.get("description"),
                   sort_order=row.get("sort_order"),
                   is_active=row.get("is_active"),
                   notes=row.get("notes"),
                   images=row.get("images"),
                   created_date=helper.mSToDate(int(row.get("createdDate"))),
                   modified_date=helper.mSToDate(int(row.get("createdDate"))),
                   username=row.get("username"))

    @classmethod
    def newStrategy(cls, name, description, notes, images, createdDate, modifiedDate, username=None):
        return cls(strat_id=None, name=name, description=description, notes=notes, images=images,
                   created_date=createdDate, modified_date=modifiedDate, display_name=name, sort_order=None,
                   is_active=True, username="None")

    def get_strategy_summary(self):
        """
        Calculates and returns a summary of trades using this strategy.
        
        Returns:
            str: A formatted string showing trade statistics
        """
        from trades_db import TradesDB
        from models.trade import TradeStatus, TradeDirection
        from decimal import Decimal

        # If strategy hasn't been saved yet (no ID), return placeholder
        if self.strat_id is None:
            return "No trades yet"

        # Get all trades from database
        trades = TradesDB.get_trades()

        # Filter trades that use this strategy
        strategy_trades = []
        for trade in trades:
            # Skip trades with no strategy information
            if not trade.strategy:
                continue

            # Parse strategy IDs from CSV format
            strategy_ids = [s.strip() for s in trade.strategy.split(',') if s.strip()]

            # Check if this strategy's ID is in the list
            if str(self.strat_id) in strategy_ids:
                # Only include closed trades for win rate calculation
                if trade.status == TradeStatus.CLOSED:
                    strategy_trades.append(trade)

        # Calculate statistics
        total_trades = len(strategy_trades)

        # Handle case with no trades
        if total_trades == 0:
            return "No closed trades"

        # Count winning trades (profit > 0)
        winning_trades = sum(1 for trade in strategy_trades if trade.profit > 0)

        # Calculate win rate percentage
        win_rate = (winning_trades / total_trades) * 100

        # Calculate additional statistics
        total_profit = sum(trade.profit for trade in strategy_trades)
        avg_profit = total_profit / total_trades if total_trades > 0 else Decimal(0)

        # Count long vs short trades
        long_trades = sum(1 for trade in strategy_trades if trade.direction == TradeDirection.LONG)
        short_trades = total_trades - long_trades

        # Calculate average trade duration
        avg_duration = sum(trade.duration for trade in strategy_trades if
                           trade.duration is not None) / total_trades if total_trades > 0 else 0
        duration_str = helper.format_duration(avg_duration) if avg_duration > 0 else ""

        # Determine best performing direction
        if long_trades > 0 and short_trades > 0:
            long_profit = sum(trade.profit for trade in strategy_trades if trade.direction == TradeDirection.LONG)
            short_profit = sum(trade.profit for trade in strategy_trades if trade.direction == TradeDirection.SHORT)
            best_direction = "Long" if long_profit > short_profit else "Short"
            direction_note = f" • {best_direction} bias"
        else:
            direction_note = ""

        # Format the basic summary
        basic_summary = f"{total_trades} Trades — {int(win_rate)}% W/R"

        # Build the complete summary with all available information
        summary_parts = [basic_summary]

        # Add duration if available
        if duration_str:
            summary_parts.append(f"{duration_str} avg")

        # Add profit information if positive
        if total_profit > 0:
            summary_parts.append(f"${int(total_profit)}")

        # Add direction bias if available
        if direction_note:
            summary_parts.append(direction_note.strip(" •"))

        # Join all parts with bullet separators
        return " • ".join(summary_parts)

    def get_last_update_time(self):
        if self.modified_date is None:
            return "Not yet used"
        delta = helper.get_now_date() - self.modified_date

        # Within a few minutes
        if delta < timedelta(minutes=1):
            return "Last updated just now"
        elif delta < timedelta(hours=1):
            minutes = delta.seconds // 60
            return f"Last updated {minutes} mins ago"

        # Within a few hours
        elif delta < timedelta(days=1):
            hours = delta.seconds // 3600
            minutes = (delta.seconds % 3600) // 60
            if minutes > 0:
                return f"Last updated {hours} hours, {minutes} minutes ago"
            else:
                return f"Last updated {hours} hours ago"

        # Yesterday
        elif delta < timedelta(days=2):
            return f"Last updated yesterday at {self.modified_date.strftime('%I:%M %p')}"

        # Within the past week
        elif delta < timedelta(weeks=1):
            return f"Last updated {self.modified_date.strftime('%A')} at {self.modified_date.strftime('%I:%M %p')}"

        # Beyond a week
        else:
            return f"Last updated {self.modified_date.strftime('%m/%d/%Y')}"

    def printInfo(self):
        print('\n''------------------- ')
        print("id: " + self.strat_id)
        print("name: " + self.name)
        print("description: " + self.description)
        print("notes: " + self.notes)
        print("images: " + self.images)
        print("createdDate: " + str(self.created_date))
        print("modifiedDate: " + str(self.modified_date))
