class TimeFrame:
    def __init__(self, time_frame_id=None, name=None, display_name=None, description=None,
                 sort_order=None, is_active=True, notes=None, created_date=None, modified_date=None, username=None):
        self.time_frame_id = time_frame_id
        self.name = name
        self.display_name = display_name
        self.description = description
        self.sort_order = sort_order
        self.is_active = is_active
        self.notes = notes
        self.created_date = created_date
        self.modified_date = modified_date
        self.username = username

    @staticmethod
    def fromRow(row):
        """Create a TimeFrame object from a database row."""
        import helper

        tf = TimeFrame()
        id_value = row.get("id")
        if isinstance(id_value, str) and id_value.startswith("tf_"):
            tf.time_frame_id = id_value.replace("tf_", "")
        else:
            tf.time_frame_id = id_value
        tf.name = row.get("name")
        tf.display_name = row.get("display_name")
        tf.description = row.get("description")
        tf.notes = row.get("notes")
        tf.images = row.get("images")
        tf.sort_order = row.get("sort_order")
        tf.is_active = bool(row.get("is_active", 1))
        tf.username = row.get("username")

        # Convert timestamps to datetime objects if they exist
        if row.get("createdDate"):
            tf.created_date = helper.mSToDate(int(row.get("createdDate")))
        if row.get("modifiedDate"):
            tf.modified_date = helper.mSToDate(int(row.get("modifiedDate")))

        tf.username = row.get("username")
        return tf
