from decimal import Decimal

import helper


class SierraActivity:
    def __init__(self, row):
        self.orderId = None  # Our Order ID after it's saved in DB
        # Use FillExecutionServiceID for fills, composite ID for others
        if row.FillExecutionServiceID:
            self.activityId = row.FillExecutionServiceID
        else:
            # Use InternalOrderID + hash of OrderActionSource + timestamp
            import hashlib
            source_hash = hashlib.md5(row.OrderActionSource.encode()).hexdigest()[:8]
            self.activityId = f"{row.InternalOrderID}_{source_hash}_{helper.date_to_ms(date=row.TransDateTime)}"
        self.activityType = row.ActivityType
        self.date = row.DateTime
        self.transDate = row.TransDateTime
        self.symbol = row.Symbol
        self.orderActionSource = row.OrderActionSource
        self.quantity = row.Quantity
        self.orderType = row.OrderType
        self.buySell = row.BuySell
        self.price = row.Price
        self.price2 = row.Price2
        self.internalOrderId = row.InternalOrderID
        self.serviceOrderId = row.ServiceOrderID
        self.orderStatus = row.OrderStatus
        self.exchangeOrderId = row.ExchangeOrderID
        self.fillPrice = row.FillPrice
        self.tradeAccount = row.TradeAccount
        self.openClose = "Close" if self.orderType == "Stop Limit" else row.OpenClose
        self.parentInternalOrderId = row.ParentInternalOrderID
        self.positionQuantity = row.PositionQuantity
        self.fillExecutionServiceId = row.FillExecutionServiceID
        self.highDuringPosition = row.HighDuringPosition
        self.lowDuringPosition = row.LowDuringPosition
        self.note = row.Note
        self.accountBalance = row.AccountBalance
        self.clientOrderId = row.ClientOrderID
        self.timeInForce = row.TimeInForce
        self.username = row.Username
        self.isAutomated = row.IsAutomated
        # Special logic since some activities will report fills even though they were not filled
        # These orderActionSource's are different from the ones we get actual fills
        self.filledQuantity = row.FilledQuantity
        if self.orderActionSource != "Teton CME Order Routing (Filled). Info: CME  (Fill)":
            self.filledQuantity = Decimal(0)
        self.printInfo()

    def printInfo(self):
        print('\n''------------------- ')
        print("symbol: " + self.symbol)
        print("orderStatus: " + self.orderStatus)
        print("orderType: " + self.orderType)
        print("buySell: " + self.buySell)
        print("openClose: " + self.openClose)
