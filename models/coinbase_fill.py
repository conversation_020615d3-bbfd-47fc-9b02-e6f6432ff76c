# commission = {str} '0.48835'
# entry_id = {str} '1f8e46d3268c08f96beea7764af4cc3a65a88f59e1fd0f1ba32375198d865c2f'
# liquidity_indicator = {str} 'MAKER'
# order_id = {str} 'e931ab1b-0ff2-4c37-8cdf-5f658c6778d4'
# price = {str} '97670'
# product_id = {str} 'BIT-31JAN25-CDE'
# retail_portfolio_id = {str} '3d4c567f-5c8a-509e-95c4-80b8ec5460df'
# sequence_timestamp = {str} '2025-01-02T17:43:00.514068Z'
# side = {str} 'SELL'
# size = {str} '1'
# size_in_quote = {bool} False
# trade_id = {str} 'a7feda52-5f0a-4576-bbbe-59de1157c5d7'
# trade_time = {str} '2025-01-02T17:43:00.499Z'
# trade_type = {str} 'FILL'
# user_id = {str} '3d4c567f-5c8a-509e-95c4-80b8ec5460df'


import helper
from decimal import Decimal
from datetime import datetime, timedelta
from collections import namedtuple

# Define a NamedTuple to store mapped data for CoinbaseFill
Fill = namedtuple("Fill", [
    "commission", "entry_id", "liquidity_indicator", "order_id",
    "price", "product_id", "retail_portfolio_id", "sequence_timestamp",
    "side", "size", "size_in_quote", "trade_id", "trade_time",
    "trade_type", "user_id", "notes"
])


def round_datetime_to_nearest_second(dt):
    if dt.microsecond >= 500_000:  # If microseconds are 500,000 or more, round up
        dt = dt + timedelta(seconds=1)
    return dt.replace(microsecond=0)  # Set microseconds to 0


def reset_seconds(dt: datetime) -> datetime:
    return dt.replace(second=0, microsecond=0)

class CoinbaseFill:
    def __init__(self, fill):
        self.orderId = None  # Our Order ID after it's saved in DB
        self.commission = abs(Decimal(fill.commission))
        self.entry_id = fill.entry_id
        self.liquidity_indicator = fill.liquidity_indicator
        self.coinbase_order_id = fill.order_id
        self.price = Decimal(fill.price)
        self.product_id = fill.product_id
        self.retail_portfolio_id = fill.retail_portfolio_id
        if fill.order_id == "6009c6ea6f7a8b0001f107c9":
            print("next")
        if isinstance(fill.sequence_timestamp, datetime):  # Check if it's already a date object
            self.sequence_timestamp = fill.sequence_timestamp
        else:
            seq_stamp = fill.sequence_timestamp
            if len(fill.sequence_timestamp) == 30:
                seq_stamp = fill.sequence_timestamp[:26] + "Z"

            self.sequence_timestamp = helper.dateStringToDate(seq_stamp, helper.infer_date_format(seq_stamp))
        self.side = fill.side
        self.size = abs(Decimal(fill.size))
        self.size_in_quote = fill.size_in_quote  # Bool
        self.trade_id = fill.trade_id
        if isinstance(fill.trade_time, datetime):  # Check if it's already a date object
            self.trade_time = fill.trade_time
        else:
            trade_stamp = fill.trade_time
            if len(fill.trade_time) == 30:
                trade_stamp = fill.trade_time[:26] + "Z"

            self.trade_time = helper.dateStringToDate(trade_stamp, helper.infer_date_format(trade_stamp))
        self.trade_type = fill.trade_type
        self.user_id = fill.user_id
        self.notes = getattr(fill, "notes", None)

    @classmethod
    def from_existing(cls, existing_order, quantity, tag="extra"):
        # Create a new instance without calling __init__
        new_order = cls.__new__(cls)

        # Copy all attributes from the existing object
        for attr, value in vars(existing_order).items():
            setattr(new_order, attr, value)

        # Modify the necessary attributes
        new_order.size = Decimal(abs(quantity))
        new_order.orderId = None
        new_order.commission = Decimal(0)  # Already tracked in parent order
        new_order.coinbase_order_id = new_order.coinbase_order_id + "-" + tag
        new_order.entry_id = new_order.entry_id + "-" + tag
        if new_order.trade_id:
            new_order.trade_id = new_order.trade_id + "-" + tag
        return new_order

    def printInfo(self):
        print('\n''------------------- ')
        print("Side: " + self.side)
        print("Size: " + str(self.size))
        print("product_id: " + self.product_id)
        print("Price: " + str(self.price))
        print("Maker/Taker: " + self.liquidity_indicator)

    @classmethod
    def merge_orders(cls, coinbase_fill_history, coinbase_statements):
        unique_orders = []
        duplicate_orders = []
        seen_orders = set()
        order_map = {}  # To store the original order
        for order in coinbase_fill_history:
            order_timestamp = reset_seconds(helper.convert_to_utc(order.sequence_timestamp))
            symbol = order.product_id
            symbol = symbol.replace("-USDC", "")
            symbol = symbol.replace("-USD", "")
            order_key = (symbol, order.size, order.side.lower(), order_timestamp)
            # order_key_second = (symbol, order.size, order.side.lower(), order_timestamp + timedelta(seconds=1))
            if order.size == Decimal('1831664'):
                print('here')

            seen_orders.add(order_key)
            # seen_orders.add(order_key_second)
            unique_orders.append(order)
            order_map[order_key] = order  # Store the original order
            # order_map[order_key_second] = order  # Store the original order

        for order in coinbase_statements:
            order_timestamp = reset_seconds(helper.convert_to_utc(order.sequence_timestamp))
            size = str(order.size)
            if size.endswith(".0"):
                size = size.replace(".0", "")
            order_key = (order.product_id, Decimal(size), order.side, order_timestamp)
            if order.size == Decimal('1831664.0'):
                print('here')

            if order_key not in seen_orders:
                seen_orders.add(order_key)
                unique_orders.append(order)
                order_map[order_key] = order  # Store the original order
            else:
                original_order = order_map[order_key]
                if original_order:
                    duplicate_orders.append((original_order, order))  # Store both original and duplicate

        return unique_orders, duplicate_orders