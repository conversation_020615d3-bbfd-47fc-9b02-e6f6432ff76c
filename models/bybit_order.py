from datetime import datetime
from decimal import Decimal

import pytz

import helper


def convert_ms_to_date(ms):
    # Convert milliseconds to seconds
    seconds = float(ms) / 1000.0
    # Create a datetime object from the timestamp
    date_time = datetime.fromtimestamp(seconds)
    # Format the datetime object into a readable string
    formatted_date = date_time.strftime('%d-%m-%Y %H:%M:%S')
    return formatted_date


def setFloatValue(value):
    if isinstance(value, float) or isinstance(value, int):
        return value
    elif len(value) > 0:
        return float(value)
    else:
        return 0.0


def convert_bybit_date(date_in_ms):
    if date_in_ms is None:
        return None

    # Convert milliseconds to UTC datetime
    utc_naive = datetime.utcfromtimestamp(date_in_ms / 1000)

    # Localize to UTC properly (timezone-aware)
    utc_aware = pytz.utc.localize(utc_naive)

    # Convert to local timezone
    local_tz = pytz.timezone("America/New_York")  # Or your preferred TZ
    local_date = utc_aware.astimezone(local_tz)

    return local_date


def clean_decimal(d):
    if d is None or d == "":
        return Decimal(0)
    return Decimal(d)


class BybitOrder:
    def __init__(self, data=None):
        self.orderId = None
        self.bybit_order_id = data["orderId"]
        self.orderLinkId = data["orderLinkId"]
        self.blockTradeId = data["blockTradeId"]
        self.symbol = data["symbol"]
        self.price = clean_decimal(data["price"])
        self.qty = clean_decimal(data["qty"])
        self.side = data["side"]
        self.isLeverage = data["isLeverage"]
        self.positionIdx = data["positionIdx"]
        self.orderStatus = data["orderStatus"]
        self.createType = data["createType"]
        self.cancelType = data["cancelType"]
        self.rejectReason = data["rejectReason"]
        self.avgPrice = clean_decimal(data["avgPrice"])
        self.leavesQty = clean_decimal(data["leavesQty"])
        self.leavesValue = clean_decimal(data["leavesValue"])
        self.cumExecQty = clean_decimal(data["cumExecQty"])
        self.cumExecValue = clean_decimal(data["cumExecValue"])
        self.cumExecFee = clean_decimal(data["cumExecFee"])
        self.timeInForce = data["timeInForce"]
        self.orderType = data["orderType"]
        self.stopOrderType = data["stopOrderType"]
        self.orderIv = data["orderIv"]
        self.triggerPrice = clean_decimal(data["triggerPrice"])
        self.takeProfit = clean_decimal(data["takeProfit"])
        self.stopLoss = clean_decimal(data["stopLoss"])
        self.tpslMode = data["tpslMode"]
        self.tpLimitPrice = clean_decimal(data["tpLimitPrice"])
        self.slLimitPrice = clean_decimal(data["slLimitPrice"])
        self.tpTriggerBy = data["tpTriggerBy"]
        self.slTriggerBy = data["slTriggerBy"]
        self.triggerDirection = data["triggerDirection"]
        self.triggerBy = data["triggerBy"]
        self.lastPriceOnCreated = clean_decimal(data["lastPriceOnCreated"])
        self.reduceOnly = bool(data["reduceOnly"])
        self.closeOnTrigger = bool(data["closeOnTrigger"])
        self.placeType = data["placeType"]
        self.smpType = data["smpType"]
        self.smpGroup = data["smpGroup"]
        self.smpOrderId = data["smpOrderId"]

        # Add username support - can be provided in data or set separately
        self.username = data.get("username", None)

        # Convert timestamps to datetime objects
        self.createdTime = helper.mSToDate(int(data.get("createdTime")), multiplier=1000) \
            if int(data.get("createdTime")) > 0 else None
        self.updatedTime = helper.mSToDate(int(data.get("updatedTime")), multiplier=1000) \
            if int(data.get("updatedTime")) > 0 else None


    def printInfo(self):
        print('\n''------------------- ')
        print("symbol: " + self.symbol)
        print("orderStatus: " + self.orderStatus)
        print("reduceOnly: " + str(self.reduceOnly))
        print("side: " + self.side)
        print("qty: " + str(self.qty))
        print("avgPrice: " + str(self.avgPrice))
        print("rejectReason: " + self.rejectReason)
        print("createType: " + self.createType)
        print("orderType: " + self.orderType)
        print("stopOrderType: " + self.stopOrderType)
        print("cancelType: " + self.cancelType)
        print("closeOnTrigger: " + str(self.closeOnTrigger))
        print("updatedTime: " + self.updatedTime)
        print("createdTime: " + self.createdTime)

# avgPrice = {float} 0.022689
# blockTradeId = {str} ''
# bybit_order_id = {str} 'f628f887-87ec-48d5-801b-485601e5f97b'
# cancelType = {str} 'UNKNOWN'
# closeOnTrigger = {bool} False
# createType = {str} 'CreateByUser'
# createdTime = {str} '1746717792130'
# cumExecFee = {float} 0.01179828
# cumExecQty = {float} 2600.0
# cumExecValue = {float} 58.9914
# isLeverage = {str} ''
# lastPriceOnCreated = {float} 0.018909
# leavesQty = {float} 0.0
# leavesValue = {float} 0.0
# orderId = {NoneType} None
# orderIv = {str} ''
# orderLinkId = {str} ''
# orderStatus = {str} 'Filled'
# orderType = {str} 'Limit'
# placeType = {str} ''
# positionIdx = {int} 0
# price = {float} 0.022689
# qty = {float} 2600.0
# reduceOnly = {bool} True
# rejectReason = {str} 'EC_NoError'
# side = {str} 'Sell'
# slLimitPrice = {float} 0.0
# slTriggerBy = {str} ''
# smpGroup = {int} 0
# smpOrderId = {str} ''
# smpType = {str} 'None'
# stopLoss = {float} 0.0
# stopOrderType = {str} ''
# symbol = {str} '1000BONKUSDT'
# takeProfit = {float} 0.0
# timeInForce = {str} 'PostOnly'
# tpLimitPrice = {float} 0.0
# tpTriggerBy = {str} ''
# tpslMode = {str} ''
# triggerBy = {str} ''
# triggerDirection = {int} 0
# triggerPrice = {float} 0.0
# updatedTime = {str} '1746788200698'
