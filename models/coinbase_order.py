# attached_order_configuration = {NoneType} None
# attached_order_id = {str} ''
# average_filled_price = {str} '348.7'
# cancel_message = {str} ''
# client_order_id = {str} '1a3bff68-a31a-4adc-a84a-cccd4c0df411'
# completion_percentage = {str} '100.00'
# created_time = {str} '2025-02-14T17:45:44.425022Z'
# edit_history = {list: 0} []
# fee = {str} ''
# filled_size = {str} '1'
# filled_value = {str} '348.7'
# is_liquidation = {bool} False
# last_fill_time = {str} '2025-02-14T17:45:44.526Z'
# leverage = {str} ''
# margin_type = {str} 'UNKNOWN_MARGIN_TYPE'
# number_of_fills = {str} '1'
# order_configuration = {OrderConfiguration} {'market_market_ioc': {'base_size': '1', 'rfq_enabled': False, 'rfq_disabled': False}}
# order_id = {str} 'd72e1feb-54c9-40ac-9093-ac4795e2a254'
# order_placement_source = {str} 'RETAIL_ADVANCED'
# order_type = {str} 'MARKET'
# originating_order_id = {str} ''
# outstanding_hold_amount = {str} '0'
# pending_cancel = {bool} False
# product_id = {str} 'BCH-28FEB25-CDE'
# product_type = {str} 'FUTURE'
# reject_message = {str} ''
# reject_reason = {str} 'REJECT_REASON_UNSPECIFIED'
# retail_portfolio_id = {str} '3d4c567f-5c8a-509e-95c4-80b8ec5460df'
# settled = {bool} True
# side = {str} 'BUY'
# size_in_quote = {bool} False
# size_inclusive_of_fees = {bool} False
# status = {str} 'FILLED'
# time_in_force = {str} 'IMMEDIATE_OR_CANCEL'
# total_fees = {str} '0.2'
# total_value_after_fees = {str} '348.9'
# trigger_status = {str} 'INVALID_ORDER_TYPE'
# user_id = {str} '3d4c567f-5c8a-509e-95c4-80b8ec5460df'


from collections import namedtuple
from decimal import Decimal

import helper

# Define a NamedTuple to store mapped data for CoinbaseOrder
Order = namedtuple("Order", [
    "attached_order_configuration", "attached_order_id", "average_filled_price", "cancel_message",
    "client_order_id", "completion_percentage", "created_time", "edit_history", "fee", "filled_size",
    "filled_value", "is_liquidation", "last_fill_time", "leverage", "margin_type", "number_of_fills",
    "order_configuration", "order_id", "order_placement_source", "order_type", "originating_order_id",
    "outstanding_hold_amount", "pending_cancel", "product_id", "product_type", "reject_message",
    "reject_reason", "retail_portfolio_id", "settled", "side", "size_in_quote", "size_inclusive_of_fees",
    "status", "time_in_force", "total_fees", "total_value_after_fees", "trigger_status", "user_id"
])


def clean_decimal(d):
    if d is None or d == "":
        return Decimal(0)
    return Decimal(d)


# Build CoinbaseOrder from API response (Object) or JSON file (Dict)
def get_attr_or_key(obj, key, default=None):
    """Get attribute from object or key from dict"""
    if hasattr(obj, 'get'):  # Dictionary-like
        return obj.get(key, default)
    else:  # Object-like
        return getattr(obj, key, default)


class CoinbaseOrder:
    def __init__(self, fill):
        self.order_id = None
        self.coinbase_order_id = get_attr_or_key(fill, "order_id")
        self.attached_order_configuration = get_attr_or_key(fill, "attached_order_configuration")
        self.attached_order_id = get_attr_or_key(fill, "attached_order_id", "")
        self.average_filled_price = clean_decimal(get_attr_or_key(fill, "average_filled_price", "0"))
        self.cancel_message = get_attr_or_key(fill, "cancel_message", "")
        self.client_order_id = get_attr_or_key(fill, "client_order_id", "")
        self.completion_percentage = clean_decimal(get_attr_or_key(fill, "completion_percentage", "0"))
        created_time = get_attr_or_key(fill, "created_time")
        if isinstance(created_time, int):
            # Loading from DB - created_time is milliseconds
            self.created_time = helper.mSToDate(created_time)
        elif created_time:
            # Loading from API - created_time is string
            self.created_time = helper.dateStringToDate(created_time, helper.infer_date_format(created_time))
        else:
            self.created_time = None
        self.edit_history = get_attr_or_key(fill, "edit_history", [])
        self.fee = clean_decimal(get_attr_or_key(fill, "fee", "0") if get_attr_or_key(fill, "fee", "") != "" else "0")
        self.filled_size = clean_decimal(get_attr_or_key(fill, "filled_size", "0"))
        self.filled_value = clean_decimal(get_attr_or_key(fill, "filled_value", "0"))
        self.is_liquidation = get_attr_or_key(fill, "is_liquidation", False)
        last_fill_time = get_attr_or_key(fill, "last_fill_time")
        if isinstance(last_fill_time, int):
            # Loading from DB - last_fill_time is milliseconds
            self.last_fill_time = helper.mSToDate(last_fill_time)
        elif isinstance(last_fill_time, str) and last_fill_time.isdigit():
            # Loading from DB where column was TEXT but contains integer milliseconds
            self.last_fill_time = helper.mSToDate(int(last_fill_time))
        elif last_fill_time:
            # Loading from API - last_fill_time is string date format
            self.last_fill_time = helper.dateStringToDate(last_fill_time, helper.infer_date_format(last_fill_time))
        else:
            self.last_fill_time = None
        self.leverage = clean_decimal(
            get_attr_or_key(fill, "leverage", "0") if get_attr_or_key(fill, "leverage", "") != "" else "0")
        self.margin_type = get_attr_or_key(fill, "margin_type", "")
        self.number_of_fills = clean_decimal(get_attr_or_key(fill, "number_of_fills", "0"))
        self.order_configuration = get_attr_or_key(fill, "order_configuration", {})
        self.order_placement_source = get_attr_or_key(fill, "order_placement_source", "")
        self.order_type = get_attr_or_key(fill, "order_type", "")
        self.originating_order_id = get_attr_or_key(fill, "originating_order_id", "")
        self.outstanding_hold_amount = get_attr_or_key(fill, "outstanding_hold_amount", "0")
        self.pending_cancel = get_attr_or_key(fill, "pending_cancel", False)
        self.product_id = get_attr_or_key(fill, "product_id", "")
        self.product_type = get_attr_or_key(fill, "product_type", "")
        self.reject_message = get_attr_or_key(fill, "reject_message", "")
        self.reject_reason = get_attr_or_key(fill, "reject_reason", "")
        self.retail_portfolio_id = get_attr_or_key(fill, "retail_portfolio_id", "")
        self.settled = get_attr_or_key(fill, "settled", False)
        self.side = get_attr_or_key(fill, "side", "")
        self.size_in_quote = get_attr_or_key(fill, "size_in_quote", False)
        self.size_inclusive_of_fees = get_attr_or_key(fill, "size_inclusive_of_fees", False)
        self.status = get_attr_or_key(fill, "status", "")
        self.time_in_force = get_attr_or_key(fill, "time_in_force", "")
        self.total_fees = clean_decimal(get_attr_or_key(fill, "total_fees", "0"))
        self.total_value_after_fees = clean_decimal(get_attr_or_key(fill, "total_value_after_fees", "0"))
        self.trigger_status = get_attr_or_key(fill, "trigger_status", "")
        self.user_id = get_attr_or_key(fill, "user_id", "")
        self.notes = get_attr_or_key(fill, "notes")

    @classmethod
    def from_existing(cls, existing_order, quantity, tag="extra"):
        # Create a new instance without calling __init__
        new_order = cls.__new__(cls)

        # Copy all attributes from the existing object
        for attr, value in vars(existing_order).items():
            setattr(new_order, attr, value)

        # Modify the necessary attributes
        new_order.filled_size = Decimal(abs(quantity))
        new_order.order_id = None
        new_order.total_fees = Decimal(0)  # Already tracked in parent order
        new_order.coinbase_order_id = new_order.coinbase_order_id + "-" + tag
        new_order.client_order_id = new_order.client_order_id + "-" + tag
        return new_order

    def printInfo(self):
        print("\n-------------------")
        print(f"Side:           {self.side}")
        print(f"Product ID:     {self.product_id}")
        print(f"Maker/Taker:    {self.order_type}")
        print(f"Order ID:       {self.order_id}")
        print(f"Client Order ID: {self.client_order_id}")
        print(f"Status:         {self.status}")
        print(f"Total Fees:     {self.total_fees}")
        print(f"Filled Size:    {self.filled_size}")
        print(f"Filled Value:   {self.filled_value}")
        print(f"Completion %:   {self.completion_percentage}")
        print(f"Created Time:   {self.created_time}")
        print(f"Last Fill Time: {self.last_fill_time}")
        print(f"User ID:        {self.user_id}")
        print(f"Reject Reason:  {self.reject_reason}")
        print(f"Leverage:       {self.leverage or 'N/A'}")  # Handle empty strings
        print(f"Margin Type:    {self.margin_type}")
        print("-------------------\n")

    def __repr__(self):
        """
        Returns a comprehensive string representation of the CoinbaseOrder object.
        
        Returns:
            str: A formatted string with key order details
        """
        # Determine fill status
        fill_status = "FILLED" if self.status == "FILLED" else "UNFILLED"

        # Format prices with 2 decimal places
        price_str = f"${self.average_filled_price:.2f}" if self.average_filled_price is not None else "N/A"

        # Build the representation string
        return (
            f"{self.product_id} {self.side} {self.filled_size} "
            f"({fill_status}) @ {price_str} "
            f"Type: {self.order_type} | Status: {self.status}"
        )
