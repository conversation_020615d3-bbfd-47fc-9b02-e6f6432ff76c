from datetime import datetime
from decimal import Decimal


class FCMPosition:
    def __init__(self, product_id, expiration_time, side, number_of_contracts, current_price, avg_entry_price,
                 unrealized_pnl, daily_realized_pnl):
        """
        Initializes an FCMPosition instance.

        :param product_id: Identifier for the product (e.g., 'BIT-31JAN25-CDE').
        :param expiration_time: Expiration time of the position (ISO 8601 format).
        :param side: The side of the position ('LONG' or 'SHORT').
        :param number_of_contracts: Number of contracts for the position.
        :param current_price: Current price of the product.
        :param avg_entry_price: Average entry price of the position.
        :param unrealized_pnl: Unrealized profit and loss.
        :param daily_realized_pnl: Realized profit and loss for the day.
        """
        self.product_id = product_id
        self.expiration_time = datetime.fromisoformat(expiration_time.replace("Z", "+00:00"))  # Parse ISO 8601
        self.side = side
        self.number_of_contracts = Decimal(number_of_contracts)
        self.current_price = Decimal(current_price)
        self.avg_entry_price = Decimal(avg_entry_price)
        self.unrealized_pnl = Decimal(unrealized_pnl)
        self.daily_realized_pnl = Decimal(daily_realized_pnl)

    @classmethod
    def from_json(cls, data):
        """
        Creates an FCMPosition instance from a JSON dictionary.

        :param data: Dictionary containing position data.
        :return: FCMPosition instance.
        """
        return cls(
            product_id=data['product_id'],
            expiration_time=data['expiration_time'],
            side=data['side'],
            number_of_contracts=data['number_of_contracts'],
            current_price=data['current_price'],
            avg_entry_price=data['avg_entry_price'],
            unrealized_pnl=data['unrealized_pnl'],
            daily_realized_pnl=data['daily_realized_pnl']
        )

    def __repr__(self):
        """
        Returns a string representation of the FCMPosition instance.
        """
        return (f"FCMPosition(product_id={self.product_id}, expiration_time={self.expiration_time}, "
                f"side={self.side}, number_of_contracts={self.number_of_contracts}, "
                f"current_price={self.current_price}, avg_entry_price={self.avg_entry_price}, "
                f"unrealized_pnl={self.unrealized_pnl}, daily_realized_pnl={self.daily_realized_pnl})")

# avg_entry_price = {float} 103190.0
# current_price = {float} 103145.0
# daily_realized_pnl = {float} 0.0
# expiration_time = {datetime} datetime.datetime(2025, 1, 31, 16, 0, tzinfo=datetime.timezone.utc)
# number_of_contracts = {int} 1
# product_id = {str} 'BIT-31JAN25-CDE'
# side = {str} 'SHORT'
# skipped_closure = {bool} False
# unrealized_pnl = {float} 1.4
