from datetime import datetime
from decimal import Decimal

import pytz

import helper


def clean_decimal(d):
    """
    Convert a value to Decimal, handling None and empty strings.

    Args:
        d: Value to convert to Decimal

    Returns:
        Decimal: Converted value or Decimal(0) if input is None or empty
    """
    if d is None or d == "":
        return Decimal(0)
    return Decimal(str(d))


class BybitPosition:
    def __init__(self, data):
        """
        Initialize a BybitPosition object from API response data.

        Args:
            data: Dictionary containing position data from Bybit API
        """
        self.raw = data
        self.symbol = data.get("symbol")

        # Map fields from the new API response format
        self.order_id = data.get("orderId")  # May be None for positions
        self.side = data.get("side")

        # Handle size/qty field (API returns "size" but we use "qty" internally)
        self.qty = clean_decimal(data.get("size", data.get("qty", 0)))

        # Handle price fields
        self.order_price = clean_decimal(0)  # Not available in position data
        self.avg_entry_price = clean_decimal(data.get("avgPrice", 0))

        # Other fields that might be used
        self.order_type = data.get("orderType")
        self.exec_type = data.get("execType")
        self.closed_size = clean_decimal(data.get("closedSize", 0))
        self.cum_entry_value = clean_decimal(data.get("positionValue", data.get("cumEntryValue", 0)))
        self.cum_exit_value = clean_decimal(data.get("cumExitValue", 0))
        self.avg_exit_price = clean_decimal(data.get("avgExitPrice", 0))
        self.closed_pnl = clean_decimal(data.get("cumRealisedPnl", data.get("closedPnl", 0)))
        self.fill_count = int(data.get("fillCount", 0)) if data.get("fillCount") else 0
        self.leverage = clean_decimal(data.get("leverage", 0))

        # Convert timestamps to datetime objects
        self.created_time = helper.mSToDate(int(data.get("createdTime")), multiplier=1000) \
            if int(data.get("createdTime")) > 0 else None
        self.updated_time = helper.mSToDate(int(data.get("updatedTime")), multiplier=1000) \
            if int(data.get("updatedTime")) > 0 else None

        # Additional fields from the new API response
        self.unrealized_pnl = clean_decimal(data.get("unrealisedPnl", 0))
        self.mark_price = clean_decimal(data.get("markPrice", 0))
        self.liq_price = clean_decimal(data.get("liqPrice", 0)) if data.get("liqPrice") and data.get(
            "liqPrice") != "" else clean_decimal(0)

        # Risk management fields
        self.position_value = clean_decimal(data.get("positionValue", 0))
        self.position_im = clean_decimal(data.get("positionIM", 0))  # Initial margin
        self.position_mm = clean_decimal(data.get("positionMM", 0))  # Maintenance margin
        self.risk_id = int(data.get("riskId", 0)) if data.get("riskId") else 0
        self.risk_limit_value = clean_decimal(data.get("riskLimitValue", 0))
        self.adl_rank_indicator = int(data.get("adlRankIndicator", 0)) if data.get("adlRankIndicator") else 0

    def direction(self):
        """
        Get the direction of the position (long or short).

        Returns:
            str: "long" if the position is a buy, "short" if it's a sell
        """
        return "long" if self.side == "Buy" else "short"

    def to_trade(self):
        """
        Convert the position to a trade dictionary.

        Returns:
            dict: A dictionary representation of the position as a trade
        """
        return {
            "symbol": self.symbol,
            "direction": self.direction(),
            "qty": str(self.qty),  # Convert Decimal to string for serialization
            "entry_price": str(self.avg_entry_price or self.order_price),  # Convert Decimal to string
            "status": "OPEN",
            "source": "bybit_position"
        }

    def get_pnl_percentage(self):
        """
        Calculate the PnL as a percentage of the position value.

        Returns:
            Decimal: PnL percentage or 0 if position value is 0
        """
        if self.position_value and self.position_value > 0:
            return (self.unrealized_pnl / self.position_value) * Decimal('100')
        return Decimal('0')

    def __str__(self):
        """
        Get a string representation of the position.

        Returns:
            str: A string representation of the position
        """
        return (f"BybitPosition({self.symbol}, {self.side}, qty={self.qty}, "
                f"avg_price={self.avg_entry_price}, unrealized_pnl={self.unrealized_pnl})")

# 'symbol' = {str} 'TRBUSDT'
# 'leverage' = {str} '10'
# 'autoAddMargin' = {int} 0
# 'avgPrice' = {str} '42.71'
# 'liqPrice' = {str} ''
# 'riskLimitValue' = {str} '200000'
# 'takeProfit' = {str} '59.67'
# 'positionValue' = {str} '17.5111'
# 'isReduceOnly' = {bool} False
# 'tpslMode' = {str} 'Full'
# 'riskId' = {int} 1
# 'trailingStop' = {str} '0'
# 'unrealisedPnl' = {str} '2.2755'
# 'markPrice' = {str} '48.26'
# 'adlRankIndicator' = {int} 4
# 'cumRealisedPnl' = {str} '-0.65634602'
# 'positionMM' = {str} '0.183779'
# 'createdTime' = {str} '0'
# 'positionIdx' = {int} 1
# 'positionIM' = {str} '1.759778'
# 'seq' = {int} 209416445302
# 'updatedTime' = {str} '1749499200016'
# 'side' = {str} 'Buy'
# 'bustPrice' = {str} ''
# 'positionBalance' = {str} '0'
# 'leverageSysUpdatedTime' = {str} ''
# 'curRealisedPnl' = {str} '-0.02035202'
# 'size' = {str} '0.41'
# 'positionStatus' = {str} 'Normal'
# 'mmrSysUpdatedTime' = {str} ''
# 'stopLoss' = {str} ''
# 'tradeMode' = {int} 0
# 'sessionAvgPrice' = {str} ''
